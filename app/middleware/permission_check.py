from functools import wraps
from flask import g, request, jsonify, abort, session, redirect, url_for, flash, current_app
from app.utils.rbac import get_user_permissions
from typing import Union, List, Optional, Callable
import logging
from app.models import User, RoutePermission

def check_permission(permissions=None):
    """
    Check if the current user has the required permission(s) to access a route.
    Can accept a single permission string or a list of permissions strings.
    
    Args:
        permissions: String or list of strings representing required permissions
        
    Returns:
        The decorated function if the user has the required permission(s),
        or a redirect to login page if not authenticated/authorized
    """
    def decorator(view_function):
        @wraps(view_function)
        def wrapper(*args, **kwargs):
            # Set up logging
            logger = logging.getLogger(__name__)
            endpoint = request.endpoint if hasattr(request, 'endpoint') else 'unknown'
            
            # If user is not logged in, redirect to login
            if 'user_id' not in session:
                flash('Please log in to access this page.', 'warning')
                return redirect(url_for('routes.login', next=request.path))
                
            # Get user ID from session
            user_id = session.get('user_id')
            
            # Get the user from the database
            user = User.query.get(user_id)
            if not user:
                flash('User not found. Please log in again.', 'danger')
                session.clear()
                return redirect(url_for('routes.login'))
            
            # Check if this endpoint has route-specific permissions
            # Import here to avoid circular imports
            from app.admin import has_route_permissions
            
            # Check if route has permissions in the database
            has_route_perms = RoutePermission.query.filter_by(endpoint=endpoint).count() > 0
            
            if has_route_perms:
                # If route has permissions, check if user has them
                if has_route_permissions(user, endpoint):
                    return view_function(*args, **kwargs)
                else:
                    flash('You do not have permission to access this page.', 'danger')
                    return redirect(url_for('routes.index'))
            
            # No decorator permissions required, allow access
            if permissions is None:
                return view_function(*args, **kwargs)
                
            # Handle single permission check
            if isinstance(permissions, str):
                has_perm = user.has_permission(permissions)
                if has_perm:
                    return view_function(*args, **kwargs)
                else:
                    flash(f'You need the "{permissions}" permission to access this page.', 'danger')
                    return redirect(url_for('routes.index'))
            
            # Handle list of permissions (ANY permission is sufficient)
            elif isinstance(permissions, list):
                # Check if user has any of the required permissions
                for perm in permissions:
                    has_perm = user.has_permission(perm)
                    if has_perm:
                        return view_function(*args, **kwargs)
                        
                # If we get here, the user doesn't have any of the required permissions
                required_perms = ', '.join(permissions)
                flash(f'You need at least one of these permissions to access this page: {required_perms}', 'danger')
                return redirect(url_for('routes.index'))
            
            # Invalid permission format
            logger.error(f"Invalid permission format: {permissions}")
            raise ValueError(f"Invalid permission format: {permissions}")
        
        return wrapper
    
    return decorator

def check_resource_permission(
    permission_name: str,
    resource_owner_id_getter: Union[str, Callable],
    allow_self: bool = True
) -> Callable:
    """
    Middleware to check permissions for resource-specific operations.
    Allows users to access their own resources and/or requires specific permissions.
    
    Args:
        permission_name: Permission required if not accessing own resource
        resource_owner_id_getter: Either a parameter name or a function to get resource owner ID
        allow_self: Whether to allow users to access their own resources without the permission
        
    Returns:
        Function decorator that checks resource permissions
        
    Usage:
        # Using parameter name
        @app.route('/users/<int:user_id>/profile')
        @check_resource_permission('user.edit', 'user_id')
        def edit_user_profile(user_id):
            return f'Editing profile {user_id}'
            
        # Using custom getter function
        def get_post_owner_id(post_id):
            post = Post.query.get(post_id)
            return post.user_id if post else None
            
        @app.route('/posts/<int:post_id>')
        @check_resource_permission('post.edit', get_post_owner_id)
        def edit_post(post_id):
            return f'Editing post {post_id}'
    """
    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not hasattr(g, 'user') or not g.user:
                return jsonify({'error': 'Authentication required'}), 401
            
            # Get the resource owner ID
            if isinstance(resource_owner_id_getter, str):
                # Get from request parameters
                owner_id = kwargs.get(resource_owner_id_getter)
            else:
                # Call the getter function with request parameters
                owner_id = resource_owner_id_getter(*args, **kwargs)
            
            if owner_id is None:
                return jsonify({'error': 'Resource not found'}), 404
            
            # Allow if it's the user's own resource and self-access is allowed
            if allow_self and str(g.user.id) == str(owner_id):
                return f(*args, **kwargs)
            
            # Check permission
            user_permissions = getattr(g, 'user_permissions', {})
            if not user_permissions:
                user_permissions = get_user_permissions(g.user)
                g.user_permissions = user_permissions
            
            if not user_permissions.get(permission_name, False):
                return jsonify({
                    'error': 'Permission denied',
                    'required_permission': permission_name,
                    'endpoint': request.endpoint,
                    'method': request.method
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator 