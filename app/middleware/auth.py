from functools import wraps
from flask import g, session, current_app, request
from app.models import User
from app.utils.rbac import get_user_permissions
from .permission_check import check_permission, check_resource_permission

def load_user():
    """
    Load user from session and store in Flask's g object.
    Also caches user permissions for the request duration.
    """
    user_id = session.get('user_id')
    g.user = None
    g.user_permissions = {}
    
    if user_id:
        user = User.query.get(user_id)
        if user and user.is_active:
            g.user = user
            # Cache permissions for this request
            g.user_permissions = get_user_permissions(user)

def init_auth_middleware(app):
    """Initialize authentication middleware"""
    
    @app.before_request
    def before_request():
        """Load user and permissions before each request"""
        load_user()
        
    @app.after_request
    def after_request(response):
        """Clean up after request"""
        # Clear request-specific data
        if hasattr(g, 'user'):
            delattr(g, 'user')
        if hasattr(g, 'user_permissions'):
            delattr(g, 'user_permissions')
        return response
        
    # Make permission decorators available at app level
    app.check_permission = check_permission
    app.check_resource_permission = check_resource_permission
    
    return app

def login_user(user):
    """
    Log in a user by storing their ID in the session.
    
    Args:
        user: User model instance
    """
    session['user_id'] = user.id
    # Optionally store additional session data
    session['user_role'] = user.role.name if user.role else None
    session['last_login'] = user.last_login.isoformat() if user.last_login else None
    
    # Update last login timestamp
    user.update_last_login()
    
    # Load user into request context
    g.user = user
    g.user_permissions = get_user_permissions(user)

def logout_user():
    """Log out the current user by clearing their session data"""
    session.clear()
    if hasattr(g, 'user'):
        delattr(g, 'user')
    if hasattr(g, 'user_permissions'):
        delattr(g, 'user_permissions')

def get_current_user():
    """Get the current user from the global context"""
    return getattr(g, 'user', None)

def get_cached_permissions():
    """Get cached permissions for the current request"""
    return getattr(g, 'user_permissions', {}) 