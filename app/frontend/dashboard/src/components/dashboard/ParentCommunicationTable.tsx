import React, { useState, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu'
import { Search, X, Filter, Phone, Check, AlertTriangle, Clock, CheckCircle2 } from 'lucide-react'
import { cn } from '@/lib/utils'

interface Student {
  id: number
  name: string
  guardianName: string
  guardianPhone: string
  lastContactDate: string | null
  status: 'Never Called' | 'Pending' | 'Overdue' | 'Called'
  daysOverdue: number | null
}

interface CommunicationItem {
  id: number
  clientName: string
  parentName: string
  lastContact: string | null
  daysOverdue: number
  priority: 'High' | 'Medium' | 'Low'
  status: 'Pending' | 'Overdue' | 'Completed' | 'Never Called'
  phone: string
  notes: string
}

interface ParentCommunicationTableProps {
  userId?: number
  isCaseManager?: boolean
  students?: Student[]
  loading?: boolean
  error?: string
}

const PriorityBadge: React.FC<{ priority: 'High' | 'Medium' | 'Low' }> = ({ priority }) => {
  const variants = {
    High: 'bg-red-500/10 text-red-600 ring-red-500/30',
    Medium: 'bg-orange-500/10 text-orange-600 ring-orange-500/30',
    Low: 'bg-gray-500/10 text-gray-600 ring-gray-500/30',
  }

  const dotColors = {
    High: 'bg-red-500',
    Medium: 'bg-orange-500',
    Low: 'bg-gray-500',
  }

  return (
    <div className={cn(
      'inline-flex items-center gap-1.5 rounded-full px-2.5 py-0.5 text-xs font-medium ring-1 ring-inset',
      variants[priority]
    )}>
      <span className={cn('mr-1 h-2 w-2 rounded-full', dotColors[priority])} />
      {priority}
    </div>
  )
}

const StatusBadge: React.FC<{ 
  status: 'Pending' | 'Overdue' | 'Completed' | 'Never Called' | 'Called'
  daysOverdue?: number | null
}> = ({ status, daysOverdue }) => {
  const isOverdue = status === 'Overdue' || (daysOverdue !== null && daysOverdue > 30)
  const actualStatus = isOverdue ? 'Overdue' : status === 'Called' ? 'Completed' : status

  const variants = {
    Pending: 'bg-blue-500/10 text-blue-600 ring-blue-500/30',
    Overdue: 'bg-red-500/10 text-red-600 ring-red-500/30',
    Completed: 'bg-emerald-500/10 text-emerald-600 ring-emerald-500/30',
    'Never Called': 'bg-gray-500/10 text-gray-600 ring-gray-500/30',
  }

  const icons = {
    Pending: <Clock className="h-3 w-3" />,
    Overdue: <AlertTriangle className="h-3 w-3" />,
    Completed: <CheckCircle2 className="h-3 w-3" />,
    'Never Called': <X className="h-3 w-3" />,
  }

  return (
    <div className={cn(
      'inline-flex items-center gap-1.5 rounded-full px-2.5 py-0.5 text-xs font-medium ring-1 ring-inset',
      variants[actualStatus]
    )}>
      {icons[actualStatus]}
      {actualStatus}
    </div>
  )
}

const ActionButton: React.FC<{
  variant: 'call' | 'complete'
  onClick: () => void
  title?: string
  disabled?: boolean
}> = ({ variant, onClick, title, disabled = false }) => {
  return (
    <Button
      size="sm"
      variant={variant === 'complete' ? 'default' : 'secondary'}
      onClick={onClick}
      title={title}
      disabled={disabled}
      className={cn(
        'h-7 px-2.5 rounded-md text-xs font-medium shadow-sm shadow-black/5',
        variant === 'complete' && 'bg-emerald-500 hover:bg-emerald-600 disabled:opacity-50'
      )}
    >
      {variant === 'call' ? (
        <>
          <Phone className="mr-1.5 h-3.5 w-3.5 opacity-80" aria-hidden="true" />
          Call
        </>
      ) : (
        <>
          <Check className="mr-1.5 h-3.5 w-3.5 opacity-80" aria-hidden="true" />
          Complete
        </>
      )}
    </Button>
  )
}

export default function ParentCommunicationTable({ userId, isCaseManager, students = [], loading, error }: ParentCommunicationTableProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [completedIds, setCompletedIds] = useState<Set<number>>(new Set())
  const [animatingIds, setAnimatingIds] = useState<Set<number>>(new Set())

  // Convert student data to communication items
  const communicationData: CommunicationItem[] = students.map(student => {
    // Determine priority based on days overdue
    let priority: 'High' | 'Medium' | 'Low' = 'Low'
    if (student.status === 'Never Called' || (student.daysOverdue && student.daysOverdue > 30)) {
      priority = 'High'
    } else if (student.daysOverdue && student.daysOverdue > 14) {
      priority = 'Medium'
    }

    // Map status
    let status: CommunicationItem['status'] = 'Pending'
    if (student.status === 'Never Called') {
      status = 'Never Called'
    } else if (student.status === 'Overdue' || (student.daysOverdue && student.daysOverdue > 30)) {
      status = 'Overdue'
    } else if (student.status === 'Called') {
      status = 'Completed'
    }

    return {
      id: student.id,
      clientName: student.name,
      parentName: student.guardianName,
      lastContact: student.lastContactDate,
      daysOverdue: student.daysOverdue || 0,
      priority,
      status,
      phone: student.guardianPhone,
      notes: student.status === 'Never Called' ? 'Initial contact needed' : 'Monthly check-in'
    }
  })

  const filteredData = useMemo(() => {
    return communicationData.filter(item => {
      // Filter out completed items
      if (completedIds.has(item.id)) {
        return false
      }
      
      const matchesSearch = searchQuery === '' || 
        item.clientName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.parentName.toLowerCase().includes(searchQuery.toLowerCase())
      
      const matchesPriority = priorityFilter === 'all' || item.priority === priorityFilter
      const matchesStatus = statusFilter === 'all' || item.status === statusFilter
      
      return matchesSearch && matchesPriority && matchesStatus
    })
  }, [searchQuery, priorityFilter, statusFilter, communicationData, completedIds])

  const handleMarkCompleted = async (id: number) => {
    // Start animation
    setAnimatingIds(prev => new Set(prev).add(id))
    
    try {
      // Make API call
      const response = await fetch('/api/dashboard/parent-communications/complete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'same-origin',
        body: JSON.stringify({ studentId: id })
      })
      
      if (!response.ok) {
        throw new Error('Failed to mark as complete')
      }
      
      // Wait a bit for animation to show
      setTimeout(() => {
        setCompletedIds(prev => new Set(prev).add(id))
        setAnimatingIds(prev => {
          const newSet = new Set(prev)
          newSet.delete(id)
          return newSet
        })
        
        // Notify parent component to update count
        if (window.dispatchEvent) {
          window.dispatchEvent(new CustomEvent('parentCommCompleted', { detail: { studentId: id } }))
        }
      }, 500)
      
    } catch (error) {
      console.error('Error marking communication as complete:', error)
      // Remove from animating state on error
      setAnimatingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(id)
        return newSet
      })
    }
  }

  const handleMakeCall = (phone: string, clientName: string) => {
    // TODO: Implement phone integration or copy to clipboard
    console.log('Initiating call to:', phone, 'for client:', clientName)
    // For now, just copy phone number to clipboard
    navigator.clipboard.writeText(phone)
  }

  const getRowClassName = (status: string, daysOverdue: number) => {
    if (status === 'Overdue' || daysOverdue > 7) {
      return 'bg-red-50 hover:bg-red-100'
    }
    if (daysOverdue > 3) {
      return 'bg-yellow-50 hover:bg-yellow-100'
    }
    return ''
  }

  const priorityCount = {
    High: communicationData.filter(item => item.priority === 'High' && item.status !== 'Completed').length,
    Medium: communicationData.filter(item => item.priority === 'Medium' && item.status !== 'Completed').length,
    Low: communicationData.filter(item => item.priority === 'Low' && item.status !== 'Completed').length,
    Overdue: communicationData.filter(item => item.status === 'Overdue' || item.daysOverdue > 7).length
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Parent Communication Manager
            </CardTitle>
            <CardDescription>
              {filteredData.length} communications requiring attention
              {priorityCount.Overdue > 0 && (
                <span className="ml-2 text-red-600 font-medium">
                  • {priorityCount.Overdue} overdue
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <div className="relative">
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search clients or parents..."
                className="pl-9 pr-9 w-64"
              />
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center justify-center pl-3 text-muted-foreground/80">
                <Search className="h-4 w-4" />
              </div>
              {searchQuery && (
                <button
                  className="absolute inset-y-0 right-0 flex h-full w-9 items-center justify-center rounded-r-lg text-muted-foreground/80 outline-offset-2 transition-colors hover:text-foreground focus:z-10 focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
                  aria-label="Clear search"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-2">
                  <Filter className="h-4 w-4 text-muted-foreground" />
                  <span>Filter</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuLabel>Priority</DropdownMenuLabel>
                <DropdownMenuCheckboxItem
                  checked={priorityFilter === 'all'}
                  onCheckedChange={() => setPriorityFilter('all')}
                >
                  All Priorities
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={priorityFilter === 'High'}
                  onCheckedChange={() => setPriorityFilter('High')}
                >
                  High ({priorityCount.High || 0})
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={priorityFilter === 'Medium'}
                  onCheckedChange={() => setPriorityFilter('Medium')}
                >
                  Medium ({priorityCount.Medium || 0})
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={priorityFilter === 'Low'}
                  onCheckedChange={() => setPriorityFilter('Low')}
                >
                  Low ({priorityCount.Low || 0})
                </DropdownMenuCheckboxItem>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>Status</DropdownMenuLabel>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === 'all'}
                  onCheckedChange={() => setStatusFilter('all')}
                >
                  All Status
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === 'Pending'}
                  onCheckedChange={() => setStatusFilter('Pending')}
                >
                  Pending
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === 'Overdue'}
                  onCheckedChange={() => setStatusFilter('Overdue')}
                >
                  Overdue
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  checked={statusFilter === 'Completed'}
                  onCheckedChange={() => setStatusFilter('Completed')}
                >
                  Completed
                </DropdownMenuCheckboxItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">
            <div className="inline-flex items-center gap-2 text-muted-foreground">
              <div className="h-5 w-5 animate-spin rounded-full border-2 border-current border-t-transparent" />
              Loading communications...
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-600">
            <AlertTriangle className="h-6 w-6 mx-auto mb-2" />
            <p>Failed to load communications</p>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Client</TableHead>
                <TableHead>Parent/Guardian</TableHead>
                <TableHead>Last Contact</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length === 0 ? (
                <TableRow>
                  <TableCell 
                    colSpan={7}
                    className="text-center py-8 text-muted-foreground"
                  >
                    No communications found matching your criteria
                  </TableCell>
                </TableRow>
              ) : (
              filteredData.map((comm) => (
                <TableRow 
                  key={comm.id}
                  className={cn(
                    getRowClassName(comm.status, comm.daysOverdue),
                    animatingIds.has(comm.id) && 'animate-fade-out-right transition-all duration-500'
                  )}
                >
                  <TableCell className="font-medium">{comm.clientName}</TableCell>
                  <TableCell>{comm.parentName}</TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      <span>
                        {comm.lastContact 
                          ? new Date(comm.lastContact).toLocaleDateString() 
                          : 'Never contacted'}
                      </span>
                      {comm.daysOverdue > 0 && (
                        <span className="text-xs text-red-600">
                          {comm.daysOverdue} days ago
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <PriorityBadge priority={comm.priority} />
                  </TableCell>
                  <TableCell>
                    <StatusBadge 
                      status={comm.status} 
                      daysOverdue={comm.daysOverdue}
                    />
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {comm.notes}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center gap-2 justify-end">
                      <ActionButton
                        variant="call"
                        onClick={() => handleMakeCall(comm.phone, comm.clientName)}
                        title="Call"
                      />
                      
                      {comm.status !== 'Completed' && (
                        <ActionButton
                          variant="complete"
                          onClick={() => handleMarkCompleted(comm.id)}
                          title="Mark as complete"
                          disabled={animatingIds.has(comm.id)}
                        />
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        )}
      </CardContent>
    </Card>
  )
}
