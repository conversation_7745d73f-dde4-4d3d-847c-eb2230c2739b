
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { StatCard } from "@/components/dashboard/StatCard";
import { FileText, Bell, User, Phone } from "lucide-react";

const DemoPage = () => {
  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold tracking-tight">Demo</h1>
      
      <Card className="bg-white">
        <CardHeader>
          <CardTitle>Component Showcase</CardTitle>
          <CardDescription>
            This page demonstrates various components that can be used throughout the application.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h2 className="text-lg font-semibold mb-4">Stat Cards</h2>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <StatCard 
                title="Notifications" 
                value="8" 
                icon={<Bell className="h-6 w-6" />} 
                iconClassName="bg-amber-100 text-amber-500"
              />
              <StatCard 
                title="Clients" 
                value="24" 
                icon={<User className="h-6 w-6" />} 
                iconClassName="bg-purple-100 text-purple-500"
              />
              <StatCard 
                title="Documents" 
                value="153" 
                icon={<FileText className="h-6 w-6" />} 
                iconClassName="bg-teal-100 text-teal-500"
              />
              <StatCard 
                title="Calls" 
                value="32" 
                icon={<Phone className="h-6 w-6" />} 
                iconClassName="bg-green-100 text-green-500"
              />
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-4">Buttons</h2>
            <div className="flex flex-wrap gap-4">
              <Button>Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="destructive">Destructive</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="link">Link</Button>
            </div>
          </div>
          
          <div>
            <h2 className="text-lg font-semibold mb-4">Cards</h2>
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>Feature 1</CardTitle>
                  <CardDescription>Description of Feature 1</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>This is some content for Feature 1.</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Feature 2</CardTitle>
                  <CardDescription>Description of Feature 2</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>This is some content for Feature 2.</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Feature 3</CardTitle>
                  <CardDescription>Description of Feature 3</CardDescription>
                </CardHeader>
                <CardContent>
                  <p>This is some content for Feature 3.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DemoPage;
