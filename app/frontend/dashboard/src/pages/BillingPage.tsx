
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { CreditCard, Download } from "lucide-react";

const BillingPage = () => {
  const invoices = [
    {
      id: "#INV-001",
      date: "May 1, 2025",
      amount: "$250.00",
      status: "Paid",
    },
    {
      id: "#INV-002",
      date: "Apr 1, 2025",
      amount: "$250.00",
      status: "Paid",
    },
    {
      id: "#INV-003",
      date: "Mar 1, 2025",
      amount: "$250.00",
      status: "Paid",
    },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold tracking-tight">Billing</h1>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Subscription</CardTitle>
            <CardDescription>Pro Plan</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$250.00/month</div>
            <p className="text-sm text-muted-foreground">
              Next billing date: June 1, 2025
            </p>
            <div className="mt-4">
              <Button>Manage Subscription</Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Payment Method</CardTitle>
            <CardDescription>Manage your payment methods</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="rounded-md bg-primary/10 p-2">
                <CreditCard className="h-6 w-6 text-primary" />
              </div>
              <div>
                <div className="font-medium">Visa •••• 4242</div>
                <div className="text-sm text-muted-foreground">
                  Expires 04/2026
                </div>
              </div>
            </div>
            <div className="mt-4">
              <Button variant="outline">Update Payment Method</Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Billing History</CardTitle>
          <CardDescription>
            View your recent invoices and payment history
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Invoice</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell>{invoice.id}</TableCell>
                  <TableCell>{invoice.date}</TableCell>
                  <TableCell>{invoice.amount}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                      {invoice.status}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button variant="ghost" size="icon">
                      <Download className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default BillingPage;
