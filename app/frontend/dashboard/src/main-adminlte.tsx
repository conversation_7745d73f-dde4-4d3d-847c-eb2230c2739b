import React from 'react';
import { createRoot } from 'react-dom/client';
import './index.css';

// Import the Dashboard component which handles all the interactivity
import Dashboard from './pages/Dashboard';

// Mount points for AdminLTE integration
document.addEventListener('DOMContentLoaded', () => {
  
  // Mount the full Dashboard component if the mount point exists
  const dashboardMount = document.getElementById('react-dashboard-mount');
  if (dashboardMount) {
    const dashboardRoot = createRoot(dashboardMount);
    
    // Get user config from window object (set by AdminLTE template)
    const config = window.dashboardConfig || {
      userId: 1,
      isCaseManager: true
    };

    // Store config globally for Dashboard to use
    window.dashboardConfig = config;

    dashboardRoot.render(
      <Dashboard />
    );
  }

  console.log('✅ React dashboard ready for AdminLTE mounting');
});
