/**
 * Service layer for stat card data fetching
 * Each stat card type has its own async data fetching method
 */

export interface StatCardData {
  count: number;
  details?: any[];
  lastUpdated?: string;
  loading?: boolean;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

class StatCardService {
  private baseUrl = '/api';

  private async fetchJson<T>(url: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(url, {
        credentials: 'same-origin',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error(`Error fetching ${url}:`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Fetch notifications count
   */
  async getNotificationsData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; notifications: any[] }>(
      `${this.baseUrl}/dashboard/notifications`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.notifications || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fetch parent/guardian communications data
   */
  async getParentCommunicationsData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; students: any[] }>(
      `${this.baseUrl}/dashboard/parent-communications/count`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.students || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fetch insurance checks data
   */
  async getInsuranceChecksData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; checks: any[] }>(
      `${this.baseUrl}/dashboard/insurance-checks`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.checks || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fetch client caseload data
   */
  async getClientCaseloadData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; clients: any[] }>(
      `${this.baseUrl}/dashboard/client-caseload`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.clients || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fetch authorization expiring data
   */
  async getAuthorizationExpiringData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; authorizations: any[] }>(
      `${this.baseUrl}/dashboard/authorization-expiring`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.authorizations || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fetch authorization expired data
   */
  async getAuthorizationExpiredData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; authorizations: any[] }>(
      `${this.baseUrl}/dashboard/authorization-expired`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.authorizations || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fetch pending staff data
   */
  async getPendingStaffData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; clients: any[] }>(
      `${this.baseUrl}/dashboard/pending-staff`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.clients || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fetch pending assignments data
   */
  async getPendingAssignmentsData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; assignments: any[] }>(
      `${this.baseUrl}/dashboard/pending-assignments`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.assignments || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Fetch provider missing info data
   */
  async getProviderMissingInfoData(): Promise<StatCardData> {
    const response = await this.fetchJson<{ count: number; providers: any[] }>(
      `${this.baseUrl}/dashboard/provider-missing-info`
    );

    if (!response.success) {
      return { count: 0, error: response.error };
    }

    return {
      count: response.data?.count || 0,
      details: response.data?.providers || [],
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get the appropriate data fetching method for a card type
   */
  getDataFetcher(cardType: string): () => Promise<StatCardData> {
    switch (cardType) {
      case 'notifications':
        return () => this.getNotificationsData();
      case 'parent-communications':
        return () => this.getParentCommunicationsData();
      case 'insurance-checks':
        return () => this.getInsuranceChecksData();
      case 'client-caseload':
        return () => this.getClientCaseloadData();
      case 'auth-expiring':
        return () => this.getAuthorizationExpiringData();
      case 'auth-expired':
        return () => this.getAuthorizationExpiredData();
      case 'pending-staff':
        return () => this.getPendingStaffData();
      case 'pending-assignments':
        return () => this.getPendingAssignmentsData();
      case 'provider-missing-info':
        return () => this.getProviderMissingInfoData();
      default:
        return async () => ({ count: 0, error: 'Unknown card type' });
    }
  }
}

export const statCardService = new StatCardService();