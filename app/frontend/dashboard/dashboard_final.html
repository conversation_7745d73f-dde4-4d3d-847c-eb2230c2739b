<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Cache control meta tags to prevent browser caching -->
  <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>Dashboard - Trinote 2.0</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="/static/adminlte/plugins/fontawesome-free/css/all.min.css">
  <!-- Chart.js -->
  <link rel="stylesheet" href="/static/adminlte/plugins/chart.js/Chart.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="/static/adminlte/dist/css/adminlte.min.css">
  
  <!-- Additional styles and scripts specific to each page -->
  
  
  <!-- Custom styles -->
  <style>
    :root {
      --trinote-primary: #14b8a6;
      --trinote-secondary: #0f766e;
      --trinote-accent: #99f6e4;
      --trinote-light: #ecfdf5;
      --trinote-gray: #f4f6f9;
      --trinote-dark: #0d5952;
      --trinote-gradient: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
    }
    
    body {
      font-family: 'Source Sans Pro', sans-serif;
      background-color: var(--trinote-gray);
    }
    
    /* Environment Notification Bar */
    .env-notification {
      background-color: #ff9800; /* Orange color */
      color: white;
      text-align: center;
      padding: 10px;
      font-weight: bold;
      position: fixed;
      width: 100%;
      top: 0;
      left: 0;
      z-index: 9999; /* Ensure it's always on top */
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .env-notification i {
      margin-right: 5px;
    }
    
    /* Adjust body padding when notification is present */
    body.has-env-notification {
      padding-top: 40px;
    }
    
    /* Adjust main header and sidebar when environment notification is present */
    body.has-env-notification .main-header {
      top: 40px;
    }
    
    body.has-env-notification .main-sidebar {
      padding-top: 40px;
    }
    
    body.has-env-notification .content-wrapper,
    body.has-env-notification .main-footer {
      margin-top: 40px;
    }
    
    /* Enhanced Navbar */
    .navbar-trinote {
      background: var(--trinote-gradient);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      border: none;
      padding: 0.5rem 1rem;
    }
    
    .navbar-light .navbar-nav .nav-link,
    .navbar-light .navbar-brand {
      color: rgba(255, 255, 255, 0.95);
      transition: all 0.2s ease;
    }
    
    .navbar-light .navbar-nav .nav-link:hover,
    .navbar-light .navbar-brand:hover {
      color: #fff;
      transform: translateY(-1px);
    }
    
    .navbar-nav .nav-item {
      margin-right: 5px;
    }
    
    .navbar-nav .nav-link {
      padding: 0.5rem 1rem;
      border-radius: 4px;
    }
    
    .navbar-nav .nav-link:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    /* Enhanced Sidebar */
    .sidebar-light-primary {
      background: linear-gradient(to bottom, #ffffff, #f8f9fa);
      box-shadow: 3px 0 10px rgba(0, 0, 0, 0.05);
      border-right: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .sidebar-light-primary .nav-sidebar .nav-item .nav-link {
      color: #444;
      border-radius: 6px;
      margin: 5px 10px;
      transition: all 0.3s ease;
      padding: 12px 15px;
    }
    
    .sidebar-light-primary .nav-sidebar .nav-item .nav-link.active {
      background: var(--trinote-gradient) !important;
      color: #fff;
      box-shadow: 0 4px 10px rgba(20, 184, 166, 0.3);
    }
    
    .sidebar-light-primary .nav-sidebar .nav-item .nav-link:hover:not(.active) {
      background-color: var(--trinote-light);
      color: var(--trinote-secondary);
      transform: translateX(3px);
    }
    
    .sidebar-light-primary .nav-sidebar .nav-item .nav-link i {
      min-width: 20px;
      text-align: center;
      margin-right: 10px;
      font-size: 1.1rem;
    }
    
    .nav-sidebar .nav-link p {
      font-weight: 500;
    }
    
    /* Brand/Logo */
    .brand-link {
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      background: linear-gradient(to right, white, #f8f9fa);
      padding: 18px 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
    }
    
    .brand-link .brand-image {
      margin-right: 0.7rem;
      margin-left: 0.5rem;
      color: var(--trinote-primary);
      font-size: 1.5rem;
      background-color: var(--trinote-light);
      padding: 8px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(20, 184, 166, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
    }

    .brand-link .brand-text {
      color: var(--trinote-dark);
      font-weight: 700;
      font-size: 1.3rem;
      letter-spacing: 0.5px;
      transition: color 0.2s;
    }
    
    .brand-link:hover .brand-text {
      color: var(--trinote-primary);
    }
    
    /* User Panel in Sidebar */
    .user-panel {
      padding: 15px 10px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
      margin-bottom: 15px;
      background: linear-gradient(to right, rgba(20, 184, 166, 0.03), rgba(20, 184, 166, 0.08));
    }
    
    .user-panel .image {
      padding-left: 5px;
    }
    
    .user-panel img {
      height: 40px;
      width: 40px;
      border: 2px solid var(--trinote-accent);
      padding: 2px;
      background-color: white;
    }
    
    .user-panel .info {
      padding: 0 10px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .user-panel .info a {
      color: var(--trinote-dark);
      font-weight: 600;
    }
    
    /* Improve Other Elements */
    .navbar .form-control-navbar {
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
    }
    
    .navbar .form-control-navbar::placeholder {
      color: rgba(255, 255, 255, 0.7);
    }
    
    .navbar .btn-navbar {
      background-color: rgba(255, 255, 255, 0.2);
      border: none;
    }
    
    /* Modern notifications */
    .dropdown-menu-lg {
      min-width: 280px;
      border-radius: 0.5rem;
      border: none;
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    }
    
    .dropdown-item {
      padding: 0.75rem 1rem;
    }
    
    .navbar-badge {
      font-size: 0.6rem;
      font-weight: 600;
      padding: 2px 5px;
      border-radius: 3px;
    }
    
    /* Improved content wrapper */
    .content-wrapper {
      background-color: var(--trinote-gray);
    }
    
    /* Card improvements */
    .card {
      border-radius: 6px;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
      margin-bottom: 20px;
    }
    
    /* Breadcrumb */
    .breadcrumb {
      background-color: transparent;
      padding: 0.3rem 0;
    }
    
    .breadcrumb-item + .breadcrumb-item::before {
      content: "›";
      color: var(--trinote-secondary);
    }
    
    .breadcrumb-item.active {
      color: var(--trinote-secondary);
      font-weight: 600;
    }
    
    /* Footer styling */
    .main-footer {
      background: white;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.02);
      padding: 12px 20px;
      color: #6c757d;
      font-weight: 400;
    }
    
    /* Avatar in navbar */
    .user-avatar {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      border: 2px solid rgba(255, 255, 255, 0.3);
      padding: 1px;
    }
    
    /* Styles for Initials/Icon Avatar */
    .user-avatar-initials {
        display: inline-flex; 
        align-items: center;
        justify-content: center;
        width: 35px;  
        height: 35px;
        border-radius: 50%;
        background-color: var(--trinote-secondary); 
        color: white; /* Icon color */
        font-size: 1rem; /* Icon size */
        /* font-weight: 600; */ /* Removed font-weight */
        vertical-align: middle; 
        margin-left: 5px; 
        margin-right: 5px; 
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .sidebar .user-avatar-initials {
        width: 40px;
        height: 40px;
        font-size: 1.1rem; /* Slightly larger icon in sidebar */
        border: 2px solid var(--trinote-accent);
        background-color: var(--trinote-primary);
        margin: 0; 
    }
    
    /* Pulse animation for sidebar active item */
    @keyframes pulse-border {
      0% {
        box-shadow: 0 0 0 0 rgba(20, 184, 166, 0.4);
      }
      70% {
        box-shadow: 0 0 0 6px rgba(20, 184, 166, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(20, 184, 166, 0);
      }
    }
    
    .sidebar-light-primary .nav-sidebar .nav-item .nav-link.active {
      animation: pulse-border 2s infinite;
    }
  </style>
  
<!-- React Components CSS -->
<link rel="stylesheet" href="/static/js/dashboard-react/assets/AppSidebar.css">

</head>
<body class="hold-transition sidebar-mini">

<div class="wrapper">

  <!-- Navbar -->
  <nav class="main-header navbar navbar-expand navbar-trinote navbar-light">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <a class="nav-link" data-widget="pushmenu" href="#" role="button">
          <i class="fas fa-bars"></i>
        </a>
      </li>
      <li class="nav-item d-none d-sm-inline-block">
        <a href="/" class="nav-link">
          <i class="fas fa-home mr-1"></i> Home
        </a>
      </li>
      <li class="nav-item d-none d-sm-inline-block">
        <a href="/?view=enhanced" class="nav-link">
          <i class="fas fa-chart-line mr-1"></i> Enhanced Dashboard
        </a>
      </li>
      <li class="nav-item d-none d-sm-inline-block">
        <a href="/dashboard" class="nav-link">
          <i class="fas fa-code mr-1"></i> React Dashboard
        </a>
      </li>
    </ul>
    
    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
      <!-- Search Box -->
      <li class="nav-item">
        <form class="form-inline">
          <div class="input-group input-group-sm">
            <input class="form-control form-control-navbar" type="search" placeholder="Search" aria-label="Search">
            <div class="input-group-append">
              <button class="btn btn-navbar" type="submit">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </form>
      </li>
      
      <!-- Messages Dropdown Menu -->
      <li class="nav-item dropdown">
        <a class="nav-link" data-toggle="dropdown" href="#">
          <i class="far fa-envelope"></i>
          <span class="badge badge-warning navbar-badge badge-messages" style="display: none;">
            
          </span>
        </a>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
          <span class="dropdown-item dropdown-header">0 New Messages</span>
          
            <span class="dropdown-item dropdown-header">No messages</span>
          
          <div class="dropdown-divider"></div>
          <a href="/messages" class="dropdown-item dropdown-footer">See All Messages</a>
          <a href="/messages/compose" class="dropdown-item dropdown-footer">Compose New Message</a>
        </div>
      </li>
      
      <!-- Notifications Dropdown Menu -->
      <li class="nav-item dropdown">
        <a class="nav-link" data-toggle="dropdown" href="#">
          <i class="far fa-bell"></i>
          <span class="badge badge-danger navbar-badge">2</span>
        </a>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
          <span class="dropdown-item dropdown-header">2 Notifications</span>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-database mr-2 text-info"></i> Database synced successfully
            <span class="float-right text-muted text-sm">3 min</span>
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-file-alt mr-2 text-warning"></i> 5 new reports available
            <span class="float-right text-muted text-sm">2 days</span>
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item dropdown-footer">See All Notifications</a>
        </div>
      </li>
      
      <!-- Fullscreen Button -->
      <li class="nav-item">
        <a class="nav-link" data-widget="fullscreen" href="#" role="button">
          <i class="fas fa-expand-arrows-alt"></i>
        </a>
      </li>
      
      <!-- User Menu -->
      <li class="nav-item dropdown">
        <a class="nav-link" data-toggle="dropdown" href="#">
          <span class="user-avatar-initials"><i class="fas fa-user"></i></span>
        </a>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
          <span class="dropdown-item dropdown-header">Admin</span>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-user mr-2"></i> My Profile
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-cog mr-2"></i> Settings
          </a>
          <div class="dropdown-divider"></div>
          <a href="/logout" class="dropdown-item text-center mt-2">
             <button type="button" class="btn btn-danger btn-sm btn-block">
                <i class="fas fa-sign-out-alt mr-1"></i> Sign Out
             </button>
          </a>
        </div>
      </li>
    </ul>
  </nav>

  <!-- Main Sidebar Container -->
  <aside class="main-sidebar sidebar-light-primary elevation-4">
    <!-- React Sidebar Mount Point -->
    <div id="react-sidebar-mount" class="h-full">
      <!-- Fallback content while React loads -->
      <div class="sidebar-fallback">
        <!-- Brand Logo -->
        <a href="/" class="brand-link">
          <i class="fas fa-clipboard-list brand-image"></i>
          <span class="brand-text">Trinote 2.0</span>
        </a>
        <div class="sidebar">
      <!-- User Panel -->
      <div class="user-panel">
        <div class="image">
          <span class="user-avatar-initials"><i class="fas fa-user"></i></span>
        </div>
        <div class="info">
          <a href="#" class="d-block">Admin</a>
          <span class="badge badge-success">Online</span>
        </div>
      </div>
      
      <!-- Enhanced Navigation with React Mount Point -->
      <nav class="mt-2">
        <!-- React Navigation Enhancer Mount Point -->
        <div id="react-nav-enhancer-mount"></div>
        
        <!-- Traditional AdminLTE Navigation -->
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
            <!-- Navigation items -->
            
            
            
            
            
            
            
            
            
            
            
            
            
            <!-- Admin Section - Requires role.view to see the dropdown -->
            
            
            
          </ul>
        </nav>
      </div>
      
      <!-- Quick Stats in Sidebar -->
      <div class="mt-4 mb-3 d-flex justify-content-center">
        <div class="px-3">
          <div class="small-box bg-info p-2 mb-3" style="border-radius: 6px;">
            <div class="inner py-2 text-center">
              <h3>Error</h3>
              <p style="margin-bottom: 0">Active Clients</p>
            </div>
          </div>
          <div class="text-center">
            <div class="btn-group">
              <button type="button" class="btn btn-sm btn-outline-info">
                <i class="fas fa-sync"></i>
              </button>
              <button type="button" class="btn btn-sm btn-outline-info">
                <i class="fas fa-chart-pie"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div><!-- End fallback content -->
  </div><!-- End react-sidebar-mount -->
  </aside>

  <!-- Content Wrapper -->
  <div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Dashboard</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              
<li class="breadcrumb-item"><a href="/">Home</a></li>
<li class="breadcrumb-item active">Dashboard</li>

            </ol>
          </div>
        </div>
      </div>
    </div>

    <!-- Main content -->
    <div class="content">
      <div class="container-fluid">
        
          
            
              <div class="alert alert-success alert-dismissible">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">&times;</button>
                <i class="icon fas fa-check"></i>
                Admin login successful (bypassed authentication)!
              </div>
            
          
        
        
<!-- React Stat Cards Row -->
<div class="row">
  <div class="col-12">
    <h4 class="mb-3">Dashboard Overview</h4>
    <!-- This div will be replaced with beautiful React stat cards -->
    <div id="react-stat-cards-mount" 
         data-stats='{"notifications": 10, "communications": 5, "insurance": 3, "caseload": 15, "authExpiring": 8, "authExpired": 2, "pendingStaff": 4, "pendingAssignments": 6, "missingInfo": 3}'></div>
  </div>
</div>

<!-- React Parent Communication Manager Row -->
<div class="row mt-4">
  <div class="col-12">
    <!-- This div will be replaced with the full Parent Communication Manager -->
    <div id="parent-communication-table-mount"></div>
  </div>
</div>

<!-- Fallback/Original Content (in case React doesn't load) -->
<div class="row mt-4" id="fallback-content" style="display: none;">
  <div class="col-lg-6">
    <div class="card card-trinote card-primary card-outline">
      <div class="card-header">
        <h3 class="card-title">Welcome</h3>
      </div>
      <div class="card-body">
        <h5>Welcome to Trinote 2.0</h5>
        <p>You are logged in successfully as <strong>admin</strong>.</p>
        <p>This system provides secure access to client information and case management tools.</p>
      </div>
    </div>
  </div>
  <div class="col-lg-6">
    <div class="card card-trinote card-primary card-outline">
      <div class="card-header">
        <h3 class="card-title">System Status</h3>
      </div>
      <div class="card-body">
        <p><i class="fas fa-check-circle text-success"></i> Application: Online</p>
        <p><i class="fas fa-check-circle text-success"></i> Database: Connected</p>
        <p><i class="fas fa-check-circle text-success"></i> Authentication: Active</p>
      </div>
    </div>
  </div>
</div>

      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="main-footer">
    <div class="float-right d-none d-sm-inline">
      Version 2.0
    </div>
    <strong>Copyright &copy; 2024 <a href="#">Trinote</a>.</strong> All rights reserved.
  </footer>
</div>

<!-- REQUIRED SCRIPTS -->
<!-- jQuery -->
<script src="/static/adminlte/plugins/jquery/jquery.min.js"></script>
<!-- AdminLTE App -->
<script src="/static/adminlte/dist/js/adminlte.min.js"></script>
<!-- Bootstrap 4 -->
<script src="/static/adminlte/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- Chart.js -->
<script src="/static/adminlte/plugins/chart.js/Chart.min.js"></script>
<!-- General Ready Block -->
<script>
  $(document).ready(function() {
    // Fade out alerts after 6 seconds
    setTimeout(function() {
      $('.alert').fadeOut('slow');
    }, 6000);
    
    // Add subtle hover effect to cards
    $('.card').hover(
      function() {
        $(this).css('transform', 'translateY(-3px)');
        $(this).css('box-shadow', '0 5px 20px rgba(0,0,0,0.1)');
      },
      function() {
        $(this).css('transform', 'translateY(0)');
        $(this).css('box-shadow', '0 0 15px rgba(0, 0, 0, 0.05)');
      }
    );
  });
</script>
<!-- Message Polling -->


<!-- Global configuration for React components -->
<script>
  // Global configuration for React sidebar and navigation components
  window.sidebarConfig = {
    userId: 1,
    username: "admin",
    permissions: [],
    unreadMessagesCount: 0,
    activeClientsCount: "Error",
    currentEndpoint: "routes.dashboard",
    apiBaseUrl: '/api'
  };
</script>

<!-- React Navigation Enhancer Scripts -->
<script type="module" src="/static/js/dashboard-react/assets/main.js"></script>
<!-- AppSidebar will be loaded as needed by the main bundle -->
<script type="module" src="/static/js/dashboard-react/assets/adminlte-components.js"></script>

<!-- Page Specific Scripts -->

<!-- React Components Configuration and JS -->
<script>
  // Configure React components before they load
  window.dashboardConfig = {
    userId: 1,
    isCaseManager: true
  };

  // Show fallback content if React components fail to load
  setTimeout(function() {
    const statCards = document.getElementById('react-stat-cards-mount');
    const parentTable = document.getElementById('parent-communication-table-mount');
    const fallback = document.getElementById('fallback-content');
    
    // If React components didn't mount (still empty), show fallback
    if (statCards && statCards.children.length === 0) {
      console.log('React components failed to load, showing fallback content');
      fallback.style.display = 'block';
    }
  }, 2000);
</script>
<script type="module" src="/static/js/dashboard-react/assets/main.js"></script>
<!-- AppSidebar will be loaded as needed by the main bundle -->
<script type="module" src="/static/js/dashboard-react/assets/adminlte-components.js"></script>

</body>
</html> 