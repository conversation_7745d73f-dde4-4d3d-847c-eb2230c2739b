<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AdminLTE + React Components Integration Example</title>
    
    <!-- AdminLTE CSS (your existing includes) -->
    <!-- ... your AdminLTE CSS files ... -->
    
    <!-- React Components CSS -->
    <link rel="stylesheet" href="/static/js/dashboard-react/assets/ParentCommunicationTable.css">
</head>
<body>
    <!-- Your AdminLTE layout structure -->
    <div class="wrapper">
        
        <!-- Your AdminLTE sidebar, header, etc. -->
        
        <!-- Main Content -->
        <div class="content-wrapper">
            <section class="content">
                
                <!-- Example: Mount React Stat Cards -->
                <div class="row">
                    <div class="col-12">
                        <h2>Dashboard Stats</h2>
                        <!-- This div will be replaced with React stat cards -->
                        <div id="react-stat-cards-mount" 
                             data-stats='{"notifications": 15, "communications": 8, "insurance": 4}'></div>
                    </div>
                </div>
                
                <!-- Example: Mount Parent Communication Table -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Parent Communications</h3>
                            </div>
                            <div class="card-body">
                                <!-- This div will be replaced with the React table -->
                                <div id="parent-communication-table-mount"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
            </section>
        </div>
    </div>
    
    <!-- Your AdminLTE JS (existing includes) -->
    <!-- ... your AdminLTE JS files ... -->
    
    <!-- React Components JS - Include these at the end -->
    <script>
        // Configure React components before they load
        window.dashboardConfig = {
            userId: 1,  // Replace with actual user ID from your backend
            isCaseManager: true  // Replace with actual user role check
        };
    </script>
    <script src="/static/js/dashboard-react/assets/ParentCommunicationTable-BTmpw2uq.js"></script>
    <script src="/static/js/dashboard-react/assets/adminlte-components.js"></script>
    
</body>
</html>
