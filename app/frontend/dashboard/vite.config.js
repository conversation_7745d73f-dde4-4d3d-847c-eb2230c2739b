import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  base: '',
  build: {
    outDir: '../../static/js/dashboard-react',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        // AdminLTE integration entry point
        main: path.resolve(__dirname, 'src/main-adminlte.tsx'),
      },
      output: {
        entryFileNames: 'assets/[name].js',
        chunkFileNames: 'assets/[name].js',  // Removed hash for predictable output
        assetFileNames: 'assets/[name][extname]'
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  }
});