#!/usr/bin/env bash

# Get the directory where this script is located
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

echo "🔍 Checking TypeScript compilation..."
pnpm build 2>&1 | head -20

echo "🔍 Checking if any files are missing..."
if [ ! -f "src/App.tsx" ]; then
  echo "❌ App.tsx is missing"
fi

if [ ! -f "src/main.tsx" ]; then
  echo "❌ main.tsx is missing"  
fi

if [ ! -f "src/components/ui/button.tsx" ]; then
  echo "❌ Button component is missing"
fi

echo "🔍 Files check complete."
