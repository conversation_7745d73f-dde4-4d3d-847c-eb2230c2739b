import logging
import time
from datetime import datetime, timedelta
from contextlib import contextmanager
import io
import csv

from flask import Blueprint, render_template, current_app, jsonify, request, session, redirect, url_for, Response, flash
from sqlalchemy import text

from app import cache
from app.auth import login_required
from app.database import get_read_only_connection
from app.middleware.permission_check import check_permission
from app.utils.encryption import decrypt_data
from app import make_cache_key  # Import the custom cache key function

# Create the blueprint with a url_prefix
clients_bp = Blueprint('clients', __name__, url_prefix='/clients')

# Use the same context manager from routes.py for database connections
@contextmanager
def safe_db_connection(timeout=10, operation_name="database operation"):
    """
    Context manager to safely handle database connections with timeouts and proper cleanup.
    
    Args:
        timeout (int): Query timeout in seconds
        operation_name (str): Name of the operation for logging purposes
        
    Yields:
        Connection: The database connection object
    """
    conn = None
    try:
        # Get connection
        conn = get_read_only_connection()
        
        # Set timeout for queries (in seconds)
        conn.timeout = timeout
        
        logging.debug(f"Database connection established with {timeout}s timeout for {operation_name}")
        
        # Yield the connection for use in the with block
        yield conn
        
    except Exception as e:
        logging.error(f"Database error during {operation_name}: {str(e)}")
        raise
    finally:
        # Always ensure connection is closed
        if conn:
            try:
                conn.close()
                logging.debug(f"Database connection closed after {operation_name}")
            except Exception as close_error:
                logging.error(f"Error closing database connection after {operation_name}: {str(close_error)}")


@clients_bp.route('/company-clients')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('client.view')
def company_clients():
    """Company clients dashboard."""
    logs = []  # Collect logs for debugging
    clients = []
    case_managers = []
    locations = []
    error_message = None
    status_filter = 'active'  # Default
    has_bcba_filter = None
    has_bt_filter = None
    conn = None  # Initialize connection to None

    try:
        # Get filters from request
        status_filter = request.args.get('status', 'active')
        case_manager_filter = request.args.get('caseManager')
        location_filter = request.args.get('location')
        has_bcba_filter = request.args.get('hasBCBA')  # New filter (yes/no/None)
        has_bt_filter = request.args.get('hasBT')  # New filter (yes/no/None)

        # Validate hasBCBA/hasBT filters
        if has_bcba_filter not in ['yes', 'no', None, '']:
            has_bcba_filter = None
        elif has_bcba_filter == '':
            has_bcba_filter = None

        if has_bt_filter not in ['yes', 'no', None, '']:
            has_bt_filter = None
        elif has_bt_filter == '':
            has_bt_filter = None

        logs.append(
            f"Starting company_clients - Status: {status_filter}, CM: {case_manager_filter}, Loc: {location_filter}, HasBCBA: {has_bcba_filter}, HasBT: {has_bt_filter}")

        # Set query timeout limit to avoid hanging connections
        QUERY_TIMEOUT = 15  # 15 seconds max for any query
        conn = get_read_only_connection()
        # Set query timeout (SQL Server specific)
        conn.timeout = QUERY_TIMEOUT
        logs.append(f"Database connection established with {QUERY_TIMEOUT}s timeout")

        try:
            # Get list of case managers for filter
            logs.append("Fetching case managers...")
            case_managers_query = text("""
                SELECT DISTINCT CONCAT(casemanager.LastName, ' ', casemanager.FirstName)
                FROM Student s
                LEFT JOIN Staff casemanager ON s.CaseManager = casemanager.LoginID
                WHERE casemanager.LoginID IS NOT NULL
                ORDER BY 1
            """)
            case_managers_sql = str(case_managers_query)
            case_managers_result = conn.execute(case_managers_sql).fetchall()
            case_managers = [cm[0] for cm in case_managers_result]
            logs.append(f"Retrieved {len(case_managers)} case managers")

            # Get list of locations for filter
            logs.append("Fetching locations...")
            locations_query = text("""
                SELECT DISTINCT TRIM(loc.OfficeLocName)
                FROM tblOfficeLocations loc
                JOIN tblStudentIntake si ON loc.OfficeLocID = si.OfficeLocID
                WHERE loc.OfficeLocName IS NOT NULL
                ORDER BY 1
            """)
            locations_sql = str(locations_query)
            locations_result = conn.execute(locations_sql).fetchall()
            locations = [l[0] for l in locations_result]
            logs.append(f"Retrieved {len(locations)} locations")

            # --- Get distinct insurance names for filter ---
            logs.append("Fetching distinct insurance names...")
            insurance_names_query = """
                WITH CurrentInsuranceLink AS (
                    SELECT
                        sil.StudentID,
                        sil.Ins_ID,
                        ROW_NUMBER() OVER (
                            PARTITION BY sil.StudentID
                            ORDER BY
                                CASE WHEN sil.SIL_EndDate IS NULL THEN 0 ELSE 1 END ASC,
                                sil.SIL_StartDate DESC
                        ) as rn
                    FROM tblStudentInsuranceLink sil
                    WHERE sil.SIL_StartDate <= GETDATE()
                      AND (sil.SIL_EndDate IS NULL OR sil.SIL_EndDate >= GETDATE())
                )
                SELECT DISTINCT ISNULL(ins.Ins_Name, 'N/A') AS InsuranceName
                FROM Student s
                JOIN tblStudentIntake si ON s.StudentID = si.SI_StudentID -- Join intake to potentially filter by client status if needed
                LEFT JOIN CurrentInsuranceLink cil ON s.StudentID = cil.StudentID AND cil.rn = 1
                LEFT JOIN tblInsurances ins ON cil.Ins_ID = ins.Ins_ID
                -- Optional: Add WHERE clause here if you only want insurances for ACTIVE clients in the filter
                -- WHERE si.DateTerminated IS NULL 
                ORDER BY InsuranceName;
            """
            insurance_names_result = conn.execute(insurance_names_query).fetchall()
            insurance_names = [row.InsuranceName for row in insurance_names_result]
            logs.append(f"Retrieved {len(insurance_names)} distinct insurance names.")
            # --- End Insurance Name Fetch ---

            # Get selected insurance filter
            insurance_name_filter = request.args.get('insuranceName')
            logs.append(
                f"Filters: Status={status_filter}, CM={case_manager_filter}, Loc={location_filter}, BCBA={has_bcba_filter}, BT={has_bt_filter}, Ins={insurance_name_filter}")  # Added Ins to log

            # Get clients data with filters
            logs.append("Building main clients query...")
            # Base query (added placeholder for insurance filter)
            base_query = """
                WITH CurrentInsuranceLink AS (
                    SELECT 
                        sil.StudentID,
                        sil.Ins_ID, -- Corrected column name
                        ROW_NUMBER() OVER (
                            PARTITION BY sil.StudentID 
                            ORDER BY 
                                CASE WHEN sil.SIL_EndDate IS NULL THEN 0 ELSE 1 END ASC, -- Prioritize active (NULL end date)
                                sil.SIL_StartDate DESC -- Then newest start date
                        ) as rn
                    FROM tblStudentInsuranceLink sil
                    WHERE sil.SIL_StartDate <= GETDATE() -- Insurance has started
                      AND (sil.SIL_EndDate IS NULL OR sil.SIL_EndDate >= GETDATE()) -- And is currently active
                )
                SELECT DISTINCT 
                    s.StudentID, s.FirstName, s.LastName, 
                    u.EmailAddress, TRIM(loc.OfficeLocName) AS Location,
                    CONCAT(casemanager.LastName, ' ', casemanager.FirstName) AS CaseManager,
                    CASE WHEN si.DateTerminated IS NULL THEN 'Active' ELSE 'Inactive' END AS Status,
                    ISNULL(ins.Ins_Name, 'N/A') AS InsuranceName -- Corrected table and column name
                FROM Student s
                LEFT JOIN Users u ON s.HomeID = u.UserID
                LEFT JOIN Staff casemanager ON s.CaseManager = casemanager.LoginID
                JOIN tblStudentIntake si ON s.StudentID = si.SI_StudentID
                JOIN tblOfficeLocations loc ON si.OfficeLocID = loc.OfficeLocID
                LEFT JOIN CurrentInsuranceLink cil ON s.StudentID = cil.StudentID AND cil.rn = 1 -- Join with CTE
                LEFT JOIN tblInsurances ins ON cil.Ins_ID = ins.Ins_ID -- Corrected table and join condition
                WHERE 1=1 
            """

            param_placeholders = []

            # Add status filter
            if status_filter == 'active':
                base_query += " AND si.DateTerminated IS NULL"
            elif status_filter == 'inactive':
                base_query += " AND si.DateTerminated IS NOT NULL"

            # Add Case Manager filter if provided
            if case_manager_filter:
                base_query += " AND CONCAT(casemanager.LastName, ' ', casemanager.FirstName) = ?"
                param_placeholders.append(case_manager_filter)
                logs.append(f"Adding Case Manager filter: {case_manager_filter}")

            # Add Location filter if provided
            if location_filter:
                base_query += " AND TRIM(loc.OfficeLocName) = ?"
                param_placeholders.append(location_filter)
                logs.append(f"Adding Location filter: {location_filter}")

            # --- Add Has Current BCBA filter --- 
            bcba_subquery = """
                EXISTS (
                    SELECT 1 FROM therapistStudentLink tsl
                    JOIN vwProviders vp ON tsl.TherapistID = vp.ProviderID
                    WHERE tsl.StudentID = s.StudentID
                    AND vp.RoleID = 4  -- BCBA role
                    AND GETDATE() BETWEEN tsl.StartDate AND ISNULL(tsl.EndDate, '9999-12-31')
                )
                """
            if has_bcba_filter == 'yes':
                base_query += f" AND {bcba_subquery}"
                logs.append("Adding Has Current BCBA = Yes filter")
            elif has_bcba_filter == 'no':
                base_query += f" AND NOT {bcba_subquery}"
                logs.append("Adding Has Current BCBA = No filter")

            # --- Add Has Current BT filter --- 
            bt_subquery = """
                 EXISTS (
                    SELECT 1 FROM therapistStudentLink tsl
                    JOIN vwProviders vp ON tsl.TherapistID = vp.ProviderID
                    WHERE tsl.StudentID = s.StudentID
                    AND vp.RoleID = 6  -- BT role
                    AND GETDATE() BETWEEN tsl.StartDate AND ISNULL(tsl.EndDate, '9999-12-31')
                )
                """
            if has_bt_filter == 'yes':
                base_query += f" AND {bt_subquery}"
                logs.append("Adding Has Current BT = Yes filter")
            elif has_bt_filter == 'no':
                base_query += f" AND NOT {bt_subquery}"
                logs.append("Adding Has Current BT = No filter")

            # --- Add Insurance Name filter --- 
            if insurance_name_filter:
                base_query += " AND ISNULL(ins.Ins_Name, 'N/A') = ?"
                param_placeholders.append(insurance_name_filter)
                logs.append(f"Adding Insurance Name filter: {insurance_name_filter}")
            # --- End Insurance Filter --- 

            base_query += " ORDER BY s.LastName, s.FirstName"

            # Execute query with parameters
            logs.append(f"Executing clients query: {base_query[:350]}... with params: {param_placeholders}")
            clients_result = conn.execute(base_query, tuple(param_placeholders))
            column_names = [desc[0] for desc in clients_result.description]
            clients = [dict(zip(column_names, row)) for row in clients_result.fetchall()]
            logs.append(f"Fetched {len(clients)} clients")
        except Exception as query_error:
            # Handle query-specific errors
            error_message = f"Database query error: {str(query_error)}"
            logs.append(error_message)
            logging.error(error_message)
            # Let the outer exception handler handle rendering the error page
            raise
        finally:
            # Always close the connection when done with queries
            if conn:
                conn.close()
                logs.append("Database connection closed")

        return render_template('active_clients.html',
                               clients=clients,
                               case_managers=case_managers,
                               locations=locations,
                               insurance_names=insurance_names,  # Pass insurance names
                               current_status=status_filter,
                               current_has_bcba=has_bcba_filter,  # Pass new filter value
                               current_has_bt=has_bt_filter,  # Pass new filter value
                               current_insurance_name=insurance_name_filter,  # Pass selected value
                               error=None,
                               debug_logs=logs)

    except Exception as e:
        error_message = f"Error loading company client data: {str(e)}"
        logs.append(error_message)
        import traceback
        logs.append(f"Traceback: {traceback.format_exc()}")
        print(error_message)  # Log to console
        logging.error(error_message)
        logging.error(f"Logs: {logs}")
        # Make sure connection is always closed, even in error cases
        if conn:
            try:
                conn.close()
                logs.append("Database connection closed due to error")
            except Exception as close_error:
                logs.append(f"Error closing connection: {str(close_error)}")
                logging.error(f"Failed to close database connection: {str(close_error)}")
        # Render the page with an error message, passing filters back if possible
        return render_template('active_clients.html',
                               error=error_message,
                               clients=[],
                               case_managers=case_managers if 'case_managers' in locals() else [],
                               locations=locations if 'locations' in locals() else [],
                               insurance_names=insurance_names if 'insurance_names' in locals() else [],
                               current_status=status_filter,
                               current_has_bcba=has_bcba_filter,
                               current_has_bt=has_bt_filter,
                               current_insurance_name=insurance_name_filter if 'insurance_name_filter' in locals() else None,
                               debug_logs=logs)


@clients_bp.route('/client-details/<int:student_id>')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('client.view')
def client_details(student_id):
    """View detailed client information including encrypted data."""
    conn = None  # Initialize connection outside try block 
    
    try:
        # Set query timeout limit to avoid hanging connections
        QUERY_TIMEOUT = 10  # 10 seconds max for any query
        conn = get_read_only_connection()
        # Set query timeout (SQL Server specific)
        conn.timeout = QUERY_TIMEOUT
        logging.debug(f"Database connection established with {QUERY_TIMEOUT}s timeout for client_details")

        # Get detailed client info including fields from Student, Users, Staff, tblStudentIntake, tblOfficeLocations, tblHomes
        client_query = text("""
            SELECT 
                s.StudentID,
                s.FirstName,
                s.MiddleName, -- Added
                s.LastName,
                s.DOB,
                s.Gender, -- Added
                s.DiagnosisCode, -- Added back
                u.EmailAddress,
                h.h_homephone AS HomePhone, -- Added
                h.h_mothercell AS MobilePhone, -- Added
                h.h_address AS Address1, -- Added
                h.h_city AS City, -- Added
                h.h_state AS State, -- Added
                h.h_zip AS ZipCode, -- Added
                TRIM(loc.OfficeLocName) AS Location,
                CONCAT(casemanager.LastName, ' ', casemanager.FirstName) AS CaseManager,
                CASE WHEN si.DateTerminated IS NULL THEN 'Active' ELSE 'Inactive' END AS Status,
                si.DateTerminated,
                si.SI_DateCreated AS ContractStartDate, -- Renamed from DateEnrolled
                si.SI_DateModified AS ContractEndDate -- Added
            FROM Student s
            LEFT JOIN Users u ON s.HomeID = u.UserID
            LEFT JOIN Staff casemanager ON s.CaseManager = casemanager.LoginID
            JOIN tblStudentIntake si ON s.StudentID = si.SI_StudentID
            JOIN tblOfficeLocations loc ON si.OfficeLocID = loc.OfficeLocID
            LEFT JOIN tblHomes h ON s.HomeID = h.h_LoginID -- Joined tblHomes
            WHERE s.StudentID = :student_id
        """)

        # Convert TextClause to string and handle parameters
        client_sql = str(client_query).replace(':student_id', str(student_id))
        client_result = conn.execute(client_sql).fetchone()

        if not client_result:
            conn.close()  # Close connection early if no client found
            logging.info(f"No client found for StudentID: {student_id}")
            return render_template('client_details.html',
                                   error="Client not found",
                                   client=None)

        # Convert row to dictionary, handling potential None values
        client_data = list(client_result)
        column_names = [desc[0] for desc in client_result.cursor_description]
        client = dict(zip(column_names, client_data))

        # Close the connection as soon as we have our data
        conn.close()
        conn = None
        logging.debug(f"Database connection closed after fetching client data")

        # Decrypt and format DOB - this doesn't need active connection
        encrypted_dob = client.get('DOB')
        decrypted_dob_str = None
        dob_formatted = 'N/A'  # Default
        if encrypted_dob and isinstance(encrypted_dob, bytes):
            try:
                decrypted_dob_str = decrypt_data(encrypted_dob)
                if decrypted_dob_str:
                    # Attempt to parse the decrypted string (handle potential time component)
                    try:
                        dob_date_str = decrypted_dob_str.strip().split(' ')[0]  # Get only the date part
                        dob_date = None
                        for fmt in ('%Y-%m-%d', '%m/%d/%Y', '%Y%m%d'):  # Try common date formats
                            try:
                                dob_date = datetime.strptime(dob_date_str, fmt).date()
                                break
                            except ValueError:
                                continue

                        if dob_date:
                            dob_formatted = dob_date.strftime('%m/%d/%Y')
                        else:
                            dob_formatted = f"Could not parse date: {dob_date_str}"  # Show failed date part
                            logging.warning(
                                f"Could not parse decrypted DOB date part '{dob_date_str}' (from '{decrypted_dob_str}') for student {student_id}")
                    except Exception as parse_err:
                        dob_formatted = "Date Parse Error"
                        logging.error(
                            f"Error parsing decrypted DOB '{decrypted_dob_str}' for student {student_id}: {parse_err}")
                else:
                    dob_formatted = "Decryption Failed"
            except Exception as decrypt_err:
                dob_formatted = "Decryption Error"
                logging.error(f"Error decrypting DOB for student {student_id}: {decrypt_err}")
        elif encrypted_dob:  # If DOB is not bytes, maybe it's already decrypted or stored differently?
            dob_formatted = str(encrypted_dob)  # Display as is, might need adjustment

        client['DOB_formatted'] = dob_formatted

        # Construct full address
        address_parts = [client.get('Address1'), client.get('City'), client.get('State'), client.get('ZipCode')]
        client['FullAddress'] = ", ".join(filter(None, address_parts))
        if not client['FullAddress']: client['FullAddress'] = 'N/A'

        return render_template('client_details.html', client=client)

    except Exception as e:
        import traceback
        error_msg = f"Error loading client details: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        logging.error(error_msg)
        
        # Ensure connection is always closed
        if conn:
            try:
                conn.close()
                logging.debug("Database connection closed in error handler")
            except Exception as close_error:
                logging.error(f"Error closing database connection: {str(close_error)}")
                
        return render_template('client_details.html', error="An error occurred loading client details.", client=None)


@clients_bp.route('/client-caseload/<int:student_id>')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('client.view')
def client_caseload(student_id):
    """View caseload for a specific client."""
    try:
        conn = get_read_only_connection()

        # Get client basic info
        client_query = text("""
            SELECT 
                s.StudentID,
                s.FirstName,
                s.LastName,
                u.EmailAddress,
                TRIM(loc.OfficeLocName) AS Location,
                CONCAT(casemanager.LastName, ' ', casemanager.FirstName) AS CaseManager,
                CASE WHEN si.DateTerminated IS NULL THEN 'Active' ELSE 'Inactive' END AS Status,
                CASE WHEN EXISTS (
                    SELECT 1 FROM therapistStudentLink tsl
                    JOIN vwProviders vp ON tsl.TherapistID = vp.ProviderID
                    WHERE tsl.StudentID = s.StudentID
                    AND vp.RoleID = 4  -- BCBA role
                    AND GETDATE() BETWEEN tsl.StartDate AND ISNULL(tsl.EndDate, '9999-12-31')
                ) THEN 1 ELSE 0 END AS HasCurrentBCBA,
                CASE WHEN EXISTS (
                    SELECT 1 FROM therapistStudentLink tsl
                    JOIN vwProviders vp ON tsl.TherapistID = vp.ProviderID
                    WHERE tsl.StudentID = s.StudentID
                    AND vp.RoleID = 6  -- BT role
                    AND GETDATE() BETWEEN tsl.StartDate AND ISNULL(tsl.EndDate, '9999-12-31')
                ) THEN 1 ELSE 0 END AS HasCurrentBT,
                s.DOB
            FROM Student s
            LEFT JOIN Users u ON s.HomeID = u.UserID
            LEFT JOIN Staff casemanager ON s.CaseManager = casemanager.LoginID
            JOIN tblStudentIntake si ON s.StudentID = si.SI_StudentID
            JOIN tblOfficeLocations loc ON si.OfficeLocID = loc.OfficeLocID
            WHERE s.StudentID = :student_id
        """)

        # Convert TextClause to string and handle parameters
        client_sql = str(client_query).replace(':student_id', str(student_id))
        client_result = conn.execute(client_sql).fetchone()

        if not client_result:
            return render_template('client_caseload.html',
                                   error="Client not found",
                                   client={'FirstName': 'Unknown', 'LastName': 'Client', 'HasCurrentBCBA': 0,
                                           'HasCurrentBT': 0},
                                   caseload=[])

        # Convert row to dictionary with confirmed fields
        client = {
            'StudentID': client_result[0],  # StudentID
            'FirstName': client_result[1],  # FirstName
            'LastName': client_result[2],  # LastName
            'EmailAddress': client_result[3],  # EmailAddress
            'Location': client_result[4],  # Location
            'CaseManager': client_result[5],  # CaseManager
            'Status': client_result[6],  # Status
            'HasCurrentBCBA': client_result[7],  # HasCurrentBCBA
            'HasCurrentBT': client_result[8],  # HasCurrentBT
            'DOB': client_result[9]  # DOB
        }

        # --- Decrypt and format DOB --- 
        encrypted_dob = client.get('DOB')
        decrypted_dob_str = None
        dob_formatted = 'N/A'  # Default
        if encrypted_dob and isinstance(encrypted_dob, bytes):
            try:
                decrypted_dob_str = decrypt_data(encrypted_dob)
                if decrypted_dob_str:
                    # Attempt to parse the decrypted string (handle potential time component)
                    try:
                        dob_date_str = decrypted_dob_str.strip().split(' ')[0]  # Get only the date part
                        dob_date = None
                        for fmt in ('%Y-%m-%d', '%m/%d/%Y', '%Y%m%d'):  # Try common date formats
                            try:
                                dob_date = datetime.strptime(dob_date_str, fmt).date()
                                break
                            except ValueError:
                                continue

                        if dob_date:
                            dob_formatted = dob_date.strftime('%m/%d/%Y')
                        else:
                            dob_formatted = f"Could not parse date: {dob_date_str}"  # Show failed date part
                            logging.warning(
                                f"Could not parse decrypted DOB date part '{dob_date_str}' (from '{decrypted_dob_str}') for student {student_id} (caseload page)")
                    except Exception as parse_err:
                        dob_formatted = "Date Parse Error"
                        logging.error(
                            f"Error parsing decrypted DOB '{decrypted_dob_str}' for student {student_id} (caseload page): {parse_err}")
                else:
                    dob_formatted = "Decryption Failed"
            except Exception as decrypt_err:
                dob_formatted = "Decryption Error"
                logging.error(f"Error decrypting DOB for student {student_id} (caseload page): {decrypt_err}")
        elif encrypted_dob:  # If DOB is not bytes, maybe it's already decrypted or stored differently?
            dob_formatted = str(encrypted_dob)  # Display as is, might need adjustment

        client['DOB_formatted'] = dob_formatted
        # --- End DOB Decryption ---

        # Get caseload information
        caseload_query = text("""
            SELECT 
                vwProviders.SortName as ProviderName,
                vwProviders.RoleName,
                CASE WHEN therapistStudentLink.TherapistID IS NULL THEN 0 ELSE 1 END AS IsCurrent,
                vwCaseload.SessionsRequired,
                vwCaseload.ProgressNotesRequired,
                therapistStudentLink.StartDate,
                therapistStudentLink.EndDate
            FROM vwCaseload
            INNER JOIN vwProviders ON vwCaseload.ProviderID = vwProviders.ProviderID
            LEFT JOIN therapistStudentLink ON vwCaseload.ProviderID = therapistStudentLink.TherapistID 
                AND vwCaseload.StudentID = therapistStudentLink.StudentID
            WHERE vwCaseload.StudentID = :student_id
        """)

        # Convert TextClause to string and handle parameters
        caseload_sql = str(caseload_query).replace(':student_id', str(student_id))
        caseload_results = conn.execute(caseload_sql).fetchall()

        # Create dictionaries using column names
        caseload = []
        for row in caseload_results:
            entry = {
                'ProviderName': row[0],  # ProviderName
                'RoleName': row[1],  # RoleName
                'IsCurrent': row[2],  # IsCurrent
                'SessionsRequired': row[3],  # SessionsRequired
                'ProgressNotesRequired': row[4],  # ProgressNotesRequired
                'StartDate': row[5],  # StartDate
                'EndDate': row[6]  # EndDate
            }
            caseload.append(entry)

        conn.close()

        return render_template('client_caseload.html',
                               client=client,
                               caseload=caseload)

    except Exception as e:
        print(f"Error retrieving client caseload data: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return render_template('client_caseload.html',
                               error=str(e),
                               client={'FirstName': 'Unknown', 'LastName': 'Client'},
                               caseload=[]) 

@clients_bp.route('/export-company-clients')
@login_required
@cache.cached(timeout=3600, key_prefix=make_cache_key)  # User-specific cache with 1-hour timeout
@check_permission('client.view')
def export_company_clients():
    """Export company clients data to CSV."""
    logs = []
    conn = None
    # Get all filters from request args
    status_filter = request.args.get('status', 'active')
    case_manager_filter = request.args.get('caseManager')
    location_filter = request.args.get('location')
    has_bcba_filter = request.args.get('hasBCBA')  # New filter
    has_bt_filter = request.args.get('hasBT')  # New filter
    insurance_name_filter = request.args.get('insuranceName')  # New filter

    # Validate hasBCBA/hasBT filters
    if has_bcba_filter not in ['yes', 'no', None, '']:
        has_bcba_filter = None
    elif has_bcba_filter == '':
        has_bcba_filter = None

    if has_bt_filter not in ['yes', 'no', None, '']:
        has_bt_filter = None
    elif has_bt_filter == '':
        has_bt_filter = None

    # Validate insurance_name_filter if necessary (e.g., check if empty string means None)
    if insurance_name_filter == '':
        insurance_name_filter = None

    logs.append(
        f"Starting export - Status: {status_filter}, CM: {case_manager_filter}, Loc: {location_filter}, HasBCBA: {has_bcba_filter}, HasBT: {has_bt_filter}, Ins={insurance_name_filter}")

    try:
        conn = get_read_only_connection()
        logs.append("Database connection established for export")

        # Base query (add CTE for insurance, removed DiagnosisCode)
        base_query = """
            WITH CurrentInsuranceLink AS (
                SELECT 
                    sil.StudentID,
                    sil.Ins_ID, -- Corrected column name
                    ROW_NUMBER() OVER (
                        PARTITION BY sil.StudentID 
                        ORDER BY 
                            CASE WHEN sil.SIL_EndDate IS NULL THEN 0 ELSE 1 END ASC, -- Prioritize active (NULL end date)
                            sil.SIL_StartDate DESC -- Then newest start date
                    ) as rn
                FROM tblStudentInsuranceLink sil
                WHERE sil.SIL_StartDate <= GETDATE() -- Insurance has started
                  AND (sil.SIL_EndDate IS NULL OR sil.SIL_EndDate >= GETDATE()) -- And is currently active
            )
            SELECT DISTINCT 
                s.StudentID, s.FirstName, s.LastName, 
                u.EmailAddress, TRIM(loc.OfficeLocName) AS Location,
                CONCAT(casemanager.LastName, ' ', casemanager.FirstName) AS CaseManager,
                CASE WHEN si.DateTerminated IS NULL THEN 'Active' ELSE 'Inactive' END AS Status,
                ISNULL(ins.Ins_Name, 'N/A') AS InsuranceName -- Corrected table and column name
            FROM Student s
            LEFT JOIN Users u ON s.HomeID = u.UserID
            LEFT JOIN Staff casemanager ON s.CaseManager = casemanager.LoginID
            JOIN tblStudentIntake si ON s.StudentID = si.SI_StudentID
            JOIN tblOfficeLocations loc ON si.OfficeLocID = loc.OfficeLocID
            LEFT JOIN CurrentInsuranceLink cil ON s.StudentID = cil.StudentID AND cil.rn = 1 -- Join with CTE
            LEFT JOIN tblInsurances ins ON cil.Ins_ID = ins.Ins_ID -- Corrected table and join condition
            WHERE 1=1 
        """

        # List to hold query parameters for safe execution
        params = []

        # Add status filter
        if status_filter == 'active':
            base_query += " AND si.DateTerminated IS NULL"
        elif status_filter == 'inactive':
            base_query += " AND si.DateTerminated IS NOT NULL"

        # Add Case Manager filter if provided
        if case_manager_filter:
            base_query += " AND CONCAT(casemanager.LastName, ' ', casemanager.FirstName) = ?"
            params.append(case_manager_filter)
            logs.append(f"Adding Case Manager filter: {case_manager_filter}")

        # Add Location filter if provided
        if location_filter:
            base_query += " AND TRIM(loc.OfficeLocName) = ?"
            params.append(location_filter)
            logs.append(f"Adding Location filter: {location_filter}")

        # --- Add Has Current BCBA filter (mirroring main route) --- 
        bcba_subquery = """
            EXISTS (
                SELECT 1 FROM therapistStudentLink tsl
                JOIN vwProviders vp ON tsl.TherapistID = vp.ProviderID
                WHERE tsl.StudentID = s.StudentID
                AND vp.RoleID = 4  -- BCBA role
                AND GETDATE() BETWEEN tsl.StartDate AND ISNULL(tsl.EndDate, '9999-12-31')
            )
            """
        if has_bcba_filter == 'yes':
            base_query += f" AND {bcba_subquery}"
            logs.append("Adding Has Current BCBA = Yes filter")
        elif has_bcba_filter == 'no':
            base_query += f" AND NOT {bcba_subquery}"
            logs.append("Adding Has Current BCBA = No filter")

        # --- Add Has Current BT filter (mirroring main route) --- 
        bt_subquery = """
             EXISTS (
                SELECT 1 FROM therapistStudentLink tsl
                JOIN vwProviders vp ON tsl.TherapistID = vp.ProviderID
                WHERE tsl.StudentID = s.StudentID
                AND vp.RoleID = 6  -- BT role
                AND GETDATE() BETWEEN tsl.StartDate AND ISNULL(tsl.EndDate, '9999-12-31')
            )
            """
        if has_bt_filter == 'yes':
            base_query += f" AND {bt_subquery}"
            logs.append("Adding Has Current BT = Yes filter")
        elif has_bt_filter == 'no':
            base_query += f" AND NOT {bt_subquery}"
            logs.append("Adding Has Current BT = No filter")

        # --- Add Insurance Name filter --- 
        if insurance_name_filter:
            base_query += " AND ISNULL(ins.Ins_Name, 'N/A') = ?"
            params.append(insurance_name_filter)
            logs.append(f"Adding Insurance Name filter: {insurance_name_filter}")
        # --- End Insurance Filter --- 

        base_query += " ORDER BY s.LastName, s.FirstName"

        logs.append(f"Executing export query: {base_query[:350]}... with params: {params}")
        # Use prepared statement parameters for security
        result = conn.execute(base_query, tuple(params))
        results = result.fetchall()
        column_names = [column[0] for column in result.description]
        logs.append(f"Fetched {len(results)} clients for export")

        # Generate CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # Write Header
        writer.writerow(column_names)

        # Write Data
        for row in results:
            writer.writerow(row)

        csv_data = output.getvalue()
        logs.append("CSV data generated successfully")

        # Create Response
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # Update filename generation
        filename_parts = ["company_clients", status_filter]
        if case_manager_filter: filename_parts.append(f"cm_{case_manager_filter.replace(' ', '_')}")
        if location_filter: filename_parts.append(f"loc_{location_filter.replace(' ', '_')}")
        if has_bcba_filter: filename_parts.append(f"bcba_{has_bcba_filter}")  # Added BCBA filter
        if has_bt_filter: filename_parts.append(f"bt_{has_bt_filter}")  # Added BT filter
        if insurance_name_filter: filename_parts.append(
            f"ins_{insurance_name_filter.replace(' ', '_').replace('/', '-')}")  # Added insurance filter
        filename_parts.append(timestamp)
        filename = "_".join(filter(None, filename_parts)) + ".csv"

        return Response(
            csv_data,
            mimetype="text/csv",
            headers={"Content-Disposition": f"attachment;filename={filename}"}
        )

    except Exception as e:
        error_message = f"Error exporting company client data: {str(e)}"
        logs.append(error_message)
        import traceback
        logs.append(f"Traceback: {traceback.format_exc()}")
        print(error_message)  # Log to console
        # Log details
        logging.error(f"Export failed: {error_message}")
        logging.error(f"Logs: {logs}")
        # Return an error message to the user (or redirect with flash)
        flash(f"Could not export data: {error_message}", "danger")
        # Pass all filters back on redirect
        return redirect(url_for('clients.company_clients',  # Updated to use clients.company_clients
                                status=status_filter,
                                caseManager=case_manager_filter,
                                location=location_filter,
                                hasBCBA=has_bcba_filter,  # Added BCBA filter
                                hasBT=has_bt_filter,
                                insuranceName=insurance_name_filter))  # Added insurance filter

    finally:
        if conn:
            conn.close()
            logs.append("Database connection closed for export") 