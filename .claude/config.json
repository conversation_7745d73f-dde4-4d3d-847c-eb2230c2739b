{"project_name": "Trinote 2.0", "description": "A modern task management system for ABA providers built with Flask", "context_directory": ".claude/context", "context_priority": ["00_project_overview.md", "01_database_schema.md", "02_code_patterns.md", "03_workflows.md", "04_security.md"], "conventions": {"python_style": "PEP 8 with 4-space indentation and 100 character line limit", "docstring_style": "Google", "naming_convention": {"variables": "snake_case", "functions": "snake_case", "classes": "PascalCase", "constants": "UPPER_SNAKE_CASE", "files": "snake_case.py", "tables": "snake_case_plural"}}, "version": "1.0.0"}