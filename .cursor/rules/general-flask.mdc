---
description: 
globs: 
alwaysApply: true
---
---
description: Enforce Flask best practices with PostgreSQL, Alembic, caching, and AdminLTE; prevent AI from auto-generating migrations
globs: app/**/*.py, migrations/**/*.py, templates/**/*.html, static/**/*, *.py
alwaysApply: true
---

- **Project Structure & Blueprints**
  - Use `Blueprint`s to modularize features like `auth`, `admin`, `routes`
  - Centralize registration in `create_app()` within `__init__.py`
  - Avoid route logic in the same file as app creation

- **Alembic Migration Rules**
  - ❌ **NEVER auto-generate or apply Alembic migrations**
  - ✅ **Only display a suggestion** like:
    ```bash
    echo "Suggested: alembic revision --autogenerate -m 'add foo column'"
    ```
  - Do not write files in `alembic/versions/`
  - Do not call `alembic upgrade` or `alembic revision` directly in any script or function

- **Caching (Flask-Caching)**
  - Use `@cache.cached(timeout=..., key_prefix='...')` for reusable endpoints
  - Don’t cache permissioned or user-specific views unless explicitly handled
  - Example:
    ```python
    @cache.cached(timeout=900, key_prefix='report-auth-status')
    def get_auth_status():
        ...
    ```

- **Database Use**
  - Use SQLAlchemy models (`db.Model`) for core logic
  - Use context-managed raw connections (`with safe_db_connection(...)`) for legacy DBs
  - Avoid raw SQL unless wrapped in `text()` with parameters
  - ❌ **Never use string formatting or f-strings in SQL**:
    ```python
    # ✅
    db.session.execute(text("SELECT * FROM users WHERE id = :id"), {'id': user_id})

    # ❌
    db.session.execute(f"SELECT * FROM users WHERE id = {user_id}")
    ```

- **RBAC & Permissions**
  - Always enforce both `@login_required` and `@check_permission('perm')` decorators
  - Use `has_permission()` logic on `User` and `Role` objects
  - Avoid hardcoded permission logic in templates or frontend JS

- **Templating (AdminLTE + Jinja2)**
  - All templates must extend `layouts/base.html`
  - Use `{% block %}` consistently and never duplicate `<head>` logic
  - Static files must use `url_for('static', filename='...')`
  - Use reusable components (`includes/`, `macros.html`) to avoid repetition

- **Logging & Health**
  - Use `logging.getLogger(__name__)` and `logger.info()` instead of `print()`
  - `/health` and `/version` routes must return valid JSON with version & DB status
  - Log startup via `=== APPLICATION STARTUP COMPLETE ===`

- **Configuration**
  - Use a `Config` class with environment variables
  - Never hardcode secrets; load with `os.getenv()`
  - Example:
    ```python
    class Config:
        SQLALCHEMY_DATABASE_URI = os.getenv("DATABASE_URL")
        SECRET_KEY = os.getenv("SECRET_KEY")
    ```

- **Best Practices Summary**
  - dont import mid file imports go at the top.
  - always check for existing import before adding imports.
  - check for unused variables before closing a file remove those variables if unsed.
  ```python
  # ✅ DO
  @cache.cached(timeout=60, key_prefix='dashboard')
  def dashboard():
      ...

  db.session.execute(text("SELECT * FROM users WHERE id = :id"), {'id': user_id})

  {% extends "layouts/base.html" %}
  {% block content %} ... {% endblock %}

  # ❌ DON'T
  alembic revision --autogenerate
  db.session.execute(f"SELECT * FROM users WHERE id = {user_id}")
  {% include "full_layout.html" %}
