---
description: Main development guidelines for the Trinote 2.0 is a modern task management system for ABA providers, built with a Python/Flask backend and a React/shadcn frontend, supporting PostgreSQL (primary) and MSSQL (legacy) databases. project
globs: **/*
alwaysApply: true
---

# Trinote 2.0 Frontend Development Guidelines

**Project:** Trinote 2.0 - Modern Task Management System for ABA Providers
**Type:** Frontend
**Date:** 6/3/2025

## 1. PROJECT OVERVIEW

Trinote 2.0 aims to provide ABA providers with a modern and efficient task management system to streamline their workflow and improve client outcomes. The frontend focuses on providing a user-friendly and responsive interface for managing tasks, schedules, and client data.

### Objectives

*   Develop a responsive and intuitive user interface.
*   Integrate seamlessly with the Python/Flask backend API.
*   Ensure accessibility and usability for all users.
*   Maintain a clean, maintainable, and scalable codebase.

### Technical Goals and Success Criteria

*   **Goal:** Implement all core task management features (creation, editing, assignment, completion).
    *   **Success Criteria:** All features are fully functional, tested, and meet performance requirements.
*   **Goal:** Achieve a Lighthouse score of 90+ for performance, accessibility, best practices, and SEO.
    *   **Success Criteria:** Consistent scores above 90 across all major browsers and devices.
*   **Goal:** Implement robust error handling and user feedback mechanisms.
    *   **Success Criteria:** All errors are gracefully handled, and users receive clear and helpful messages.

### Recommended Technology Stack

*   **React:** 18.x
*   **shadcn/ui:** Latest (based on Radix UI primitives)
*   **TypeScript:** 4.x or 5.x
*   **Vite:** 4.x or 5.x
*   **ESLint:** 8.x
*   **Prettier:** 2.x or 3.x
*   **Testing Library:** 13.x
*   **Jest:** 28.x or 29.x
*   **React Router Dom:** 6.x
*   **Zustand/Redux Toolkit:** (State Management - choose one based on team preference. Zustand is recommended for its simplicity)

### Architectural Patterns and Design Decisions

*   **Component-Based Architecture:** Break down the UI into reusable components.
*   **Atomic Design Principles:** Structure components based on atoms, molecules, organisms, templates, and pages.
*   **Separation of Concerns:** Separate data fetching, business logic, and presentation logic.
*   **State Management:** Use Zustand or Redux Toolkit for managing global application state.  Zustand is preferred for smaller projects due to its simplicity.
*   **API Integration:** Use React Query or SWR for efficient data fetching and caching. React Query is recommended for its robust features.
*   **UI Library:** shadcn/ui is preferred because it provides unstyled components that can be easily customized.

## 2. CODE STRUCTURE AND ORGANIZATION

### File/Folder Structure

```
frontend/
├── src/
│   ├── components/        # Reusable UI components
│   │   ├── atoms/           # Basic building blocks (buttons, inputs, labels)
│   │   ├── molecules/       # Simple compositions of atoms (form fields)
│   │   ├── organisms/       # Complex compositions of molecules and atoms (task lists)
│   │   ├── templates/       # Page layouts
│   │   └── pages/           # Specific pages (task dashboard, user profile)
│   ├── hooks/             # Custom React hooks
│   ├── utils/             # Utility functions (date formatting, API helpers)
│   ├── services/          # API service implementations
│   ├── context/           # React Context providers (e.g., authentication, theme)
│   ├── styles/            # Global styles and theme definitions
│   ├── App.tsx            # Main application component
│   ├── main.tsx           # Entry point for the application
│   └── vite-env.d.ts      # TypeScript environment definitions
├── public/            # Static assets (images, fonts)
├── .eslintrc.cjs       # ESLint configuration
├── .prettierrc.cjs     # Prettier configuration
├── vite.config.ts      # Vite configuration
├── tsconfig.json        # TypeScript configuration
└── package.json         # Project dependencies
```

### Naming Conventions

*   **Components:** PascalCase (e.g., `TaskItem.tsx`, `TaskList.tsx`)
*   **Variables:** camelCase (e.g., `taskName`, `handleSubmit`)
*   **Constants:** UPPER_SNAKE_CASE (e.g., `API_ENDPOINT`, `MAX_LENGTH`)
*   **Functions:** camelCase (e.g., `formatDate`, `fetchTasks`)
*   **Files:** kebab-case (e.g., `task-item.tsx`, `api-helpers.ts`)

**Good Example:**

```typescript
// TaskItem.tsx
interface TaskItemProps {
  task: {
    id: string;
    name: string;
    description: string;
    completed: boolean;
  };
  onTaskUpdate: (id: string, completed: boolean) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, onTaskUpdate }) => {
  const handleComplete = () => {
    onTaskUpdate(task.id, !task.completed);
  };

  return (
    <div>
      <h3>{task.name}</h3>
      <p>{task.description}</p>
      <button onClick={handleComplete}>{task.completed ? 'Mark Incomplete' : 'Mark Complete'}</button>
    </div>
  );
};

export default TaskItem;
```

**Bad Example:**

```javascript
// taskItem.js
function Task(props) {
  return (
    <div>
      <h3>{props.task.name}</h3>
      <p>{props.task.description}</p>
      <button onClick={() => props.updateTask(props.task.id)}></button>
    </div>
  );
}

export default Task;
```

*   **Explanation:** The good example follows PascalCase for component names, camelCase for variables, and includes TypeScript type definitions for props. The bad example uses a generic component name (`Task`), lacks type definitions, and has an unclear prop update function.

### Module Organization and Dependency Management

*   Use `npm` or `yarn` for dependency management.
*   Declare all dependencies in `package.json`.
*   Avoid unnecessary dependencies.
*   Organize modules based on feature or functionality.

### State Management Patterns

*   **Zustand (Recommended):** A small, fast, and scalable bearbones state-management solution.
*   **Redux Toolkit:** A robust and comprehensive state management library.

**Zustand Example:**

```typescript
// store.ts
import { create } from 'zustand';

interface TaskState {
  tasks: { id: string; name: string; completed: boolean }[];
  addTask: (name: string) => void;
  updateTask: (id: string, completed: boolean) => void;
}

const useTaskStore = create<TaskState>((set) => ({
  tasks: [],
  addTask: (name) =>
    set((state) => ({
      tasks: [...state.tasks, { id: Math.random().toString(), name, completed: false }],
    })),
  updateTask: (id, completed) =>
    set((state) => ({
      tasks: state.tasks.map((task) => (task.id === id ? { ...task, completed } : task)),
    })),
}));

export default useTaskStore;

// Component using the store
import useTaskStore from './store';

const TaskList = () => {
  const tasks = useTaskStore((state) => state.tasks);
  const addTask = useTaskStore((state) => state.addTask);
  const updateTask = useTaskStore((state) => state.updateTask);

  return (
    <div>
      {tasks.map((task) => (
        <div key={task.id}>
          {task.name} - {task.completed ? 'Complete' : 'Incomplete'}
          <button onClick={() => updateTask(task.id, !task.completed)}>Toggle</button>
        </div>
      ))}
      <button onClick={() => addTask('New Task')}>Add Task</button>
    </div>
  );
};

export default TaskList;

```

## 3. CODING STANDARDS

### Language-Specific Best Practices (TypeScript)

*   Use TypeScript for all frontend code.
*   Define types for all variables, function parameters, and return values.
*   Utilize interfaces and type aliases.
*   Enforce strict null checks.
*   Use generics for reusable components and functions.

**Good Example:**

```typescript
interface User {
  id: string;
  name: string;
  email: string;
}

const fetchUser = async (id: string): Promise<User> => {
  const response = await fetch(`/api/users/${id}`);
  const user: User = await response.json();
  return user;
};
```

**Bad Example:**

```javascript
const fetchUser = async (id) => {
  const response = await fetch(`/api/users/${id}`);
  const user = await response.json();
  return user;
};
```

*   **Explanation:** The good example uses TypeScript to define the `User` interface and specifies the types for the `id` parameter and the return value of `fetchUser`. The bad example lacks any type information, making it harder to understand and maintain.

### Error Handling and Logging Strategies

*   Use `try...catch` blocks for error handling.
*   Log errors to the console or a logging service.
*   Provide user-friendly error messages.
*   Implement fallback mechanisms for critical operations.

**Example:**

```typescript
try {
  const user = await fetchUser(userId);
  // ... use user data
} catch (error: any) {
  console.error('Failed to fetch user:', error);
  alert('Failed to load user data. Please try again later.');
}
```

### Performance Optimization Techniques

*   **Code Splitting:** Use dynamic imports to load code on demand.
*   **Memoization:** Use `React.memo` or `useMemo` to prevent unnecessary re-renders.
*   **Virtualization:** Use libraries like `react-window` for rendering large lists.
*   **Image Optimization:** Optimize images for web use (size and format).
*   **Lazy Loading:** Load images and other resources only when they are visible.

**Example:**

```typescript
// Using React.memo to prevent unnecessary re-renders
interface TaskItemProps {
  task: { id: string; name: string; completed: boolean };
  onTaskUpdate: (id: string, completed: boolean) => void;
}

const TaskItem: React.FC<TaskItemProps> = React.memo(({ task, onTaskUpdate }) => {
  // ... component logic
  return (
    <div>
      <h3>{task.name}</h3>
      <button onClick={() => onTaskUpdate(task.id, !task.completed)}>Toggle</button>
    </div>
  );
});

export default TaskItem;
```

### Security Implementation Guidelines

*   **Input Validation:** Validate all user inputs on the frontend and backend.
*   **Output Encoding:** Encode data before rendering it to prevent XSS attacks.
*   **Authentication and Authorization:** Implement secure authentication and authorization mechanisms.
*   **CSRF Protection:** Use CSRF tokens to protect against cross-site request forgery.
*   **Regular Security Audits:** Conduct regular security audits to identify and address vulnerabilities.

### Code Review Checklist

*   Code follows coding standards and naming conventions.
*   Code is well-documented.
*   Error handling is implemented correctly.
*   Performance optimizations are considered.
*   Security vulnerabilities are addressed.
*   Tests are written and passing.
*   Code is readable and maintainable.

## 4. DEVELOPMENT WORKFLOW

### Git Workflow

*   Use Git for version control.
*   Follow the **_Gitflow branching model_**.  Use feature branches for new features and bug fixes.
*   Create pull requests for all code changes.

### Branch Naming Rules

*   `feature/<feature-name>` (e.g., `feature/add-task-form`)
*   `bugfix/<bug-description>` (e.g., `bugfix/fix-login-error`)
*   `hotfix/<hotfix-description>` (e.g., `hotfix/urgent-security-patch`)

### Commit Message Format

*   Use the **_Conventional Commits_** format.

```
<type>(<scope>): <short description>

[optional body]

[optional footer(s)]
```

**Examples:**

*   `feat(task): Add task creation form`
*   `fix(auth): Fix login error`
*   `docs(readme): Update README with onboarding instructions`
*   `chore(deps): Upgrade React to v18`

### PR Template and Review Process

*   Use a pull request template to ensure all necessary information is included.
*   Assign reviewers to each pull request.
*   Address all review comments before merging the pull request.

**Example PR Template:**

```markdown
## Description

[Describe the purpose of this pull request.]

## Related Issues

[List any related issues or tickets.]

## Changes Made

[List the changes made in this pull request.]

## Testing

[Describe the testing performed to ensure the changes are working correctly.]

## Checklist

- [ ] Code follows coding standards
- [ ] Code is well-documented
- [ ] Error handling is implemented
- [ ] Performance is considered
- [ ] Security is considered
- [ ] Tests are written and passing

## Screenshots (if applicable)

[Include screenshots or GIFs to demonstrate the changes.]
```

### CI/CD Pipeline Configuration

*   Use a CI/CD pipeline to automate testing, building, and deployment.
*   Configure the pipeline to run tests on every commit.
*   Automate the deployment process to staging and production environments.
*   Use tools like GitHub Actions, CircleCI, or Jenkins.

### Environment Management

*   Use environment variables to configure the application for different environments.
*   Store environment variables securely.
*   Use `.env` files for local development.
*   Use separate environments for development, staging, and production.

## 5. TESTING REQUIREMENTS

### Test Pyramid Implementation

*   **Unit Tests:** Focus on testing individual components and functions.
*   **Integration Tests:** Focus on testing the interaction between components.
*   **E2E Tests:** Focus on testing the entire application flow.

### Framework Setup and Configuration

*   Use Jest and Testing Library for unit and integration tests.
*   Use Playwright or Cypress for E2E tests.

**Example Jest Configuration (jest.config.js):**

```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  transform: {
    '^.+\\.(ts|tsx|js|jsx)$': 'babel-jest',
  },
};
```

### Test Coverage Goals and Metrics

*   Aim for a test coverage of at least **_80%_**.
*   Use code coverage tools to track test coverage.
*   Focus on testing critical functionality and edge cases.

### Mocking and Test Data Strategies

*   Use mocking libraries like `jest.mock` to isolate components during testing.
*   Create realistic test data for different scenarios.
*   Avoid using real API endpoints in tests.

**Example:**

```typescript
// Mocking a function
jest.mock('./api', () => ({
  fetchTasks: jest.fn(() => Promise.resolve([{ id: '1', name: 'Task 1', completed: false }])),
}));

import { fetchTasks } from './api';
import TaskList from './TaskList';

it('should render tasks', async () => {
  const { findByText } = render(<TaskList />);
  expect(await findByText('Task 1')).toBeInTheDocument();
});
```

### E2E Testing Approach

*   Use Playwright or Cypress to simulate user interactions.
*   Write tests that cover critical user flows (e.g., login, task creation, task completion).
*   Run E2E tests in a CI/CD pipeline.

## 6. DOCUMENTATION STANDARDS

### Code Documentation Templates

*   Use JSDoc or TSDoc for documenting code.
*   Document all functions, components, and classes.
*   Include descriptions, parameters, and return values.

**Example:**

```typescript
/**
 * Fetches a user from the API.
 *
 * @param {string} id - The ID of the user to fetch.
 * @returns {Promise<User>} A promise that resolves to the user object.
 */
const fetchUser = async (id: string): Promise<User> => {
  // ... function implementation
};
```

### API Documentation Format

*   Use OpenAPI (Swagger) to document the API.
*   Include endpoint descriptions, request parameters, and response schemas.
*   Use a tool like Swagger UI to visualize the API documentation.

### README Structure and Content

*   Include a clear and concise description of the project.
*   Provide instructions for setting up the development environment.
*   Explain how to run the application.
*   Include a list of dependencies.
*   Provide information on how to contribute to the project.

### Architectural Decision Records (ADRs)

*   Document all significant architectural decisions.
*   Use a template to ensure consistency.
*   Include the context, problem, decision, and consequences.

### Deployment Documentation

*   Document the deployment process.
*   Include instructions for configuring the environment.
*   Explain how to deploy the application to different environments.
*   Provide information on how to monitor and troubleshoot the application.

## 7. QUALITY ASSURANCE

### Code Quality Metrics

*   Maintainability
*   Readability
*   Testability
*   Security
*   Performance

### Static Analysis Tools

*   Use ESLint and Prettier to enforce code style and identify potential issues.
*   Configure static analysis tools to run automatically in the CI/CD pipeline.

### Performance Monitoring

*   Use tools like New Relic or Datadog to monitor application performance.
*   Track key metrics such as response time, error rate, and resource usage.
*   Set up alerts to notify developers of performance issues.

### Security Scanning

*   Use security scanning tools to identify vulnerabilities in the codebase.
*   Address all identified vulnerabilities promptly.
*   Conduct regular security audits.

### Accessibility Guidelines

*   Follow the WCAG (Web Content Accessibility Guidelines) to ensure accessibility.
*   Use semantic HTML.
*   Provide alternative text for images.
*   Ensure sufficient color contrast.
*   Make the application keyboard-accessible.
*   Use ARIA attributes to enhance accessibility.

## 8. FILE ORGANIZATION

*   **`src/components`**:  Contains reusable UI components.  Further divided into `atoms`, `molecules`, `organisms`, `templates`, and `pages` to reflect Atomic Design principles.
*   **`src/hooks`**:  Contains custom React hooks for reusable logic.
*   **`src/utils`**: Contains utility functions for tasks like date formatting, string manipulation, and other common operations.
*   **`src/services`**: Contains code for interacting with backend APIs.  Abstracts away the details of API calls.
*   **`src/context`**:  Contains React Context providers for managing global state or shared data.
*   **`src/styles`**:  Contains global CSS styles, theme definitions, and any CSS modules.
*   **`src/App.tsx`**: The main application component that renders the overall UI structure.
*   **`src/main.tsx`**:  The entry point of the React application, responsible for rendering the `App` component into the DOM.
*   **`public`**:  Contains static assets like images, fonts, and other files that don't need to be processed by the build tool.

**Example of Correct File Placement:**

*   `src/components/atoms/Button.tsx`: A basic button component.
*   `src/components/molecules/SearchInput.tsx`: A search input field composed of a label and an input.
*   `src/components/organisms/TaskList.tsx`: A list of tasks fetched from the API.
*   `src/pages/TaskDashboard.tsx`:  The main task dashboard page.
*   `src/hooks/useFetchTasks.ts`: A custom hook for fetching tasks from the API.
*   `src/utils/date-formatter.ts`: A utility function for formatting dates.
*   `src/services/task-service.ts`:  Handles API calls related to tasks.

## 9. ONBOARDING PROCESS

### Step-by-Step Guide for New Developers

1.  **Get Access:** Request access to the project repository and necessary tools.
2.  **Clone the Repository:** Clone the project repository to your local machine.
    ```bash
    git clone <repository-url>
    ```
3.  **Install Dependencies:** Install the project dependencies using `npm` or `yarn`.
    ```bash
    npm install
    # or
    yarn install
    ```
4.  **Configure Environment:** Create a `.env` file and configure the necessary environment variables.
5.  **Run the Application:** Start the development server.
    ```bash
    npm run dev
    # or
    yarn dev
    ```
6.  **Explore the Codebase:** Familiarize yourself with the project structure and coding standards.
7.  **Run Tests:** Run the unit and integration tests to ensure the application is working correctly.
    ```bash
    npm run test
    # or
    yarn test
    ```
8.  **Contribute:** Start working on assigned tasks and follow the development workflow.

### Required Development Environment Setup

*   **Node.js:** 16.x or 18.x
*   **npm:** 8.x or 9.x / **yarn:** 1.x
*   **Git:** Latest version
*   **VS Code:** Recommended editor with ESLint, Prettier, and TypeScript extensions.

### Access Management and Permissions

*   Use GitHub teams to manage access to the project repository.
*   Grant appropriate permissions based on roles and responsibilities.
*   Follow the principle of least privilege.

### Communication Channels and Protocols

*   Use Slack for real-time communication.
*   Use Jira for task management and issue tracking.
*   Use email for formal communication.
*   Attend daily stand-up meetings to discuss progress and challenges.
*   Use code reviews to ensure code quality and knowledge sharing.

## 10. DEPLOYMENT STRATEGY

### Environment Configuration

*   Configure the production environment with the necessary environment variables.
*   Use a secure configuration management system to store sensitive information.
*   Ensure the production environment is properly secured.

### Release Process

1.  **Create a Release Branch:** Create a release branch from the `main` branch.
    ```bash
    git checkout -b release/<version-number>
    ```
2.  **Update Version Number:** Update the version number in `package.json`.
3.  **Build the Application:** Build the application for production.
    ```bash
    npm run build
    # or
    yarn build
    ```
4.  **Test the Release:** Thoroughly test the release in a staging environment.
5.  **Merge the Release Branch:** Merge the release branch into the `main` branch and tag the release.
    ```bash
    git checkout main
    git merge release/<version-number>
    git tag v<version-number>
    git push origin main --tags
    ```
6.  **Deploy to Production:** Deploy the built application to the production environment.

### Rollback Procedures

*   Implement a rollback mechanism to revert to the previous version in case of issues.
*   Use a tool like blue/green deployments to minimize downtime during rollbacks.
*   Document the rollback procedure clearly.

### Monitoring and Alerting Setup

*   Set up monitoring and alerting to track application health and performance.
*   Monitor key metrics such as response time, error rate, and resource usage.
*   Configure alerts to notify developers of issues.
*   Use a tool like New Relic, Datadog, or Prometheus.

---

Powered by tuncer-byte. Check out our projects on [GitHub](https://github.com/tuncer-byte).


---
*Powered by tuncer-byte*
*GitHub: @tuncer-byte*